{
	"extends": [
		"@n8n/typescript-config/tsconfig.common.json",
		"@n8n/typescript-config/tsconfig.backend.json"
	],
	"compilerOptions": {
		"baseUrl": ".",
		"paths": {
			"@utils/*": ["./utils/*"],
			"@nodes-testing/*": ["../../core/nodes-testing/*"]
		},
		"tsBuildInfoFile": "dist/typecheck.tsbuildinfo",
		"emitDecoratorMetadata": true,
		"experimentalDecorators": true,
		// TODO: remove all options below this line
		"useUnknownInCatchVariables": false
	},
	"include": [
		"credentials/**/*.ts",
		"nodes/**/*.ts",
		"nodes/**/*.json",
		"test/**/*.ts",
		"types/**/*.ts",
		"utils/**/*.ts"
	],
	"references": [
		{ "path": "../../core/tsconfig.build.json" },
		{ "path": "../../nodes-base/tsconfig.build.json" },
		{ "path": "../../workflow/tsconfig.build.json" }
	]
}
