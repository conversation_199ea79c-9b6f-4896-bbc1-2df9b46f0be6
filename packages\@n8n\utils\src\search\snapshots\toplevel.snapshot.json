[{"uuid": "1f619834-c927-4373-9127-e2f0b55c5dc9", "key": "n8n-nodes-base.actionNetwork", "subcategory": "*", "properties": {"displayName": "Action Network", "defaults": {"name": "Action Network"}, "description": "Consume the Action Network API", "name": "n8n-nodes-base.actionNetwork", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ActionNetwork/actionNetwork.svg", "outputs": ["main"], "codex": {"categories": ["Sales", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.actionnetwork/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/actionNetwork/"}]}}}, "type": "node"}, {"uuid": "cd9410cd-5f57-4682-8904-abc725e3dcdf", "key": "n8n-nodes-base.activeCampaign", "subcategory": "*", "properties": {"displayName": "ActiveCampaign", "defaults": {"name": "ActiveCampaign"}, "description": "Handle ActiveCampaign events via webhooks", "name": "n8n-nodes-base.activeCampaign", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/ActiveCampaign/activeCampaign.svg", "dark": "icons/n8n-nodes-base/dist/nodes/ActiveCampaign/activeCampaign.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.activecampaign/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/activeCampaign/"}]}}}, "type": "node"}, {"uuid": "9c3241e8-5b3d-473d-9255-714c303bb797", "key": "n8n-nodes-base.adalo", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>"}, "description": "Consume Adalo API", "name": "n8n-nodes-base.adalo", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Adalo/adalo.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.adalo/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/adalo/"}]}}}, "type": "node"}, {"uuid": "5564822d-b6a2-480a-86a8-c776e13877f3", "key": "n8n-nodes-base.affinity", "subcategory": "*", "properties": {"displayName": "Affinity", "defaults": {"name": "Affinity"}, "description": "Handle Affinity events via webhooks", "name": "n8n-nodes-base.affinity", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Affinity/affinity.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Affinity/affinity.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.affinity/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/affinity/"}]}}}, "type": "node"}, {"uuid": "4b1a4968-7b4e-4ae3-84b0-548e07a2988e", "key": "n8n-nodes-base.agileCrm", "subcategory": "*", "properties": {"displayName": "Agile CRM", "defaults": {"name": "Agile CRM"}, "description": "Consume Agile CRM API", "name": "n8n-nodes-base.agileCrm", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/AgileCrm/agilecrm.png", "outputs": ["main"], "codex": {"categories": ["Marketing", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.agilecrm/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/agileCrm/"}]}}}, "type": "node"}, {"uuid": "6d8d67ef-6fc9-4354-9396-ff0635489ba1", "key": "n8n-nodes-base.airtable", "subcategory": "*", "properties": {"displayName": "Airtable", "defaults": {"name": "Airtable"}, "description": "Starts the workflow when Airtable events occur", "name": "n8n-nodes-base.airtable", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Airtable/airtable.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.airtable/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/airtable/"}]}}}, "type": "node"}, {"uuid": "24d236e7-dbdb-4775-9756-64de5c8c1ec8", "key": "n8n-nodes-base.aiTransform", "subcategory": "Data Transformation", "properties": {"displayName": "AI Transform", "defaults": {"name": "AI Transform"}, "description": "Modify data based on instructions written in plain english", "name": "n8n-nodes-base.aiTransform", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/AiTransform/aitransform.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["code", "Javascript", "JS", "<PERSON><PERSON><PERSON>", "Custom Code", "Function", "AI", "LLM"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.aitransform/"}]}}}, "type": "node"}, {"uuid": "015f3a02-8ea7-4aa4-852f-2233519b4ad5", "key": "n8n-nodes-base.amqp", "subcategory": "*", "properties": {"displayName": "AMQP Sender", "defaults": {"name": "AMQP Sender"}, "description": "Sends a raw-message via AMQP 1.0, executed once per item", "name": "n8n-nodes-base.amqp", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Amqp/amqp.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.amqp/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/amqp/"}]}}}, "type": "node"}, {"uuid": "92f1b882-401c-47ff-b30a-b63e74975ab3", "key": "n8n-nodes-base.apiTemplateIo", "subcategory": "*", "properties": {"displayName": "APITemplate.io", "defaults": {"name": "APITemplate.io"}, "description": "Consume the APITemplate.io API", "name": "n8n-nodes-base.apiTemplateIo", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ApiTemplateIo/apiTemplateIo.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.apitemplateio/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/apiTemplateIo/"}]}}}, "type": "node"}, {"uuid": "eb070b41-f0f6-455c-9c9a-905d2768677a", "key": "n8n-nodes-base.asana", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>"}, "description": "Starts the workflow when Asana events occur.", "name": "n8n-nodes-base.asana", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Asana/asana.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.asana/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/asana/"}]}}}, "type": "node"}, {"uuid": "c18089c8-0c9d-4a7c-bfa9-15c73bfbc951", "key": "n8n-nodes-base.autopilot", "subcategory": "*", "properties": {"displayName": "Autopilot", "defaults": {"name": "Autopilot"}, "description": "Handle Autopilot events via webhooks", "name": "n8n-nodes-base.autopilot", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Autopilot/autopilot.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.autopilot/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/autopilot/"}]}}}, "type": "node"}, {"uuid": "9e1cb987-4bb1-4610-a52e-c992e55914da", "key": "n8n-nodes-base.awsLambda", "subcategory": "*", "properties": {"displayName": "AWS Lambda", "defaults": {"name": "AWS Lambda"}, "description": "Invoke functions on AWS Lambda", "name": "n8n-nodes-base.awsLambda", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/lambda.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awslambda/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "caae32b5-4acc-4cce-8e26-a927f97c5d0f", "key": "n8n-nodes-base.awsSns", "subcategory": "*", "properties": {"displayName": "AWS SNS", "defaults": {"name": "AWS SNS"}, "description": "Handle AWS SNS events via webhooks", "name": "n8n-nodes-base.awsSns", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/sns.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awssns/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "60b477cc-28c7-4acf-9a00-583a0ae24cad", "key": "n8n-nodes-base.awsCertificateManager", "subcategory": "*", "properties": {"displayName": "AWS Certificate Manager", "defaults": {"name": "AWS Certificate Manager"}, "description": "Sends data to AWS Certificate Manager", "name": "n8n-nodes-base.awsCertificateManager", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/CertificateManager/acm.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awscertificatemanager/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "1a7bede6-5d0b-4e86-918c-bf302c6441b7", "key": "n8n-nodes-base.awsComprehend", "subcategory": "*", "properties": {"displayName": "AWS Comprehend", "defaults": {"name": "AWS Comprehend"}, "description": "Sends data to Amazon Comprehend", "name": "n8n-nodes-base.awsComprehend", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/Comprehend/comprehend.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awscomprehend/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "ab3680b7-91f0-4761-b9d3-a434b82cac3a", "key": "n8n-nodes-base.awsDynamoDb", "subcategory": "*", "properties": {"displayName": "AWS DynamoDB", "defaults": {"name": "AWS DynamoDB"}, "description": "Consume the AWS DynamoDB API", "name": "n8n-nodes-base.awsDynamoDb", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/DynamoDB/dynamodb.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awsdynamodb/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "6b8f1935-578a-4272-b045-0d8ed47c50f1", "key": "n8n-nodes-base.awsElb", "subcategory": "*", "properties": {"displayName": "AWS ELB", "defaults": {"name": "AWS ELB"}, "description": "Sends data to AWS ELB API", "name": "n8n-nodes-base.awsElb", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/ELB/elb.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awselb/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "3d53bda1-02bc-4003-be6a-167043bb1faf", "key": "n8n-nodes-base.awsRekognition", "subcategory": "*", "properties": {"displayName": "AWS Rekognition", "defaults": {"name": "AWS Rekognition"}, "description": "Sends data to AWS Rekognition", "name": "n8n-nodes-base.awsRekognition", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/Rekognition/rekognition.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awsrekognition/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "4986c12c-8e1e-4794-b735-f784fff69ecf", "key": "n8n-nodes-base.awsS3", "subcategory": "*", "properties": {"displayName": "AWS S3", "defaults": {"name": "AWS S3"}, "description": "Sends data to AWS S3", "name": "n8n-nodes-base.awsS3", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/S3/s3.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awss3/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "24c8adc9-0c67-4fa2-a1a7-0ecf40717ba8", "key": "n8n-nodes-base.awsSes", "subcategory": "*", "properties": {"displayName": "AWS SES", "defaults": {"name": "AWS SES"}, "description": "Sends data to AWS SES", "name": "n8n-nodes-base.awsSes", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/SES/ses.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awsses/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "8f4a4416-2e93-4a2f-9793-92b1632e1a90", "key": "n8n-nodes-base.awsSqs", "subcategory": "*", "properties": {"displayName": "AWS SQS", "defaults": {"name": "AWS SQS"}, "description": "Sends messages to AWS SQS", "name": "n8n-nodes-base.awsSqs", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/SQS/sqs.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awssqs/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "14046cca-abaa-4bb8-9369-2641b36cf9ed", "key": "n8n-nodes-base.awsTextract", "subcategory": "*", "properties": {"displayName": "AWS Textract", "defaults": {"name": "AWS Textract"}, "description": "Sends data to Amazon Textract", "name": "n8n-nodes-base.awsTextract", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/Textract/textract.svg", "outputs": ["main"], "codex": {"categories": ["Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awstextract/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "2fae9303-6047-42be-8d00-4b1488639ab4", "key": "n8n-nodes-base.awsTranscribe", "subcategory": "*", "properties": {"displayName": "AWS Transcribe", "defaults": {"name": "AWS Transcribe"}, "description": "Sends data to AWS Transcribe", "name": "n8n-nodes-base.awsTranscribe", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Aws/Transcribe/transcribe.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.awstranscribe/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/aws/"}]}}}, "type": "node"}, {"uuid": "18ac7782-5b00-4bcf-a4be-fa83f62cce8f", "key": "n8n-nodes-base.bambooHr", "subcategory": "*", "properties": {"displayName": "BambooHR", "defaults": {"name": "BambooHR"}, "description": "Consume BambooHR API", "name": "n8n-nodes-base.bambooHr", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/BambooHr/bambooHr.png", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.bamboohr/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/bambooHr/"}]}}}, "type": "node"}, {"uuid": "6a5756ea-013e-4016-875a-50a434822568", "key": "n8n-nodes-base.bannerbear", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Consume Bannerbear API", "name": "n8n-nodes-base.bannerbear", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bannerbear/bannerbear.png", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.bannerbear/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/bannerbear/"}]}}}, "type": "node"}, {"uuid": "bedb2a7a-9282-4b03-8b13-65d235f6805e", "key": "n8n-nodes-base.baserow", "subcategory": "*", "properties": {"displayName": "Baserow", "defaults": {"name": "Baserow"}, "description": "Consume the Baserow API", "name": "n8n-nodes-base.baserow", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Baserow/baserow.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.baserow/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/baserow/"}]}}}, "type": "node"}, {"uuid": "85b60a44-255a-4776-91a2-af31611022d4", "key": "n8n-nodes-base.beeminder", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Consume Beeminder API", "name": "n8n-nodes-base.beeminder", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Beeminder/beeminder.png", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.beeminder/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/beeminder/"}]}}}, "type": "node"}, {"uuid": "48423483-64d1-4877-87ef-0401cacc24aa", "key": "n8n-nodes-base.bitly", "subcategory": "*", "properties": {"displayName": "Bitly", "defaults": {"name": "Bitly"}, "description": "Consume Bitly API", "name": "n8n-nodes-base.bitly", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bitly/bitly.svg", "outputs": ["main"], "codex": {"categories": ["Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.bitly/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/bitly/"}]}}}, "type": "node"}, {"uuid": "57491147-747b-44cc-a435-7cae26e183d7", "key": "n8n-nodes-base.bitwarden", "subcategory": "*", "properties": {"displayName": "Bitwarden", "defaults": {"name": "Bitwarden"}, "description": "Consume the Bitwarden API", "name": "n8n-nodes-base.bitwarden", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bitwarden/bitwarden.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.bitwarden/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/bitwarden/"}]}}}, "type": "node"}, {"uuid": "551fb1f6-ef67-42f0-a83a-6daa46ee68dc", "key": "n8n-nodes-base.box", "subcategory": "*", "properties": {"displayName": "Box", "defaults": {"name": "Box"}, "description": "Starts the workflow when Box events occur", "name": "n8n-nodes-base.box", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Box/box.png", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.box/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/box/"}]}}}, "type": "node"}, {"uuid": "33bfd2b2-4c2a-4d24-af4a-5d756faf8474", "key": "n8n-nodes-base.Brandfetch", "subcategory": "*", "properties": {"displayName": "Brandfetch", "defaults": {"name": "Brandfetch"}, "description": "Consume Brandfetch API", "name": "n8n-nodes-base.Brandfetch", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Brandfetch/brandfetch.png", "outputs": ["main"], "codex": {"categories": ["Utility", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.brandfetch/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/brandfetch/"}]}}}, "type": "node"}, {"uuid": "6d4059de-4a12-47e9-999c-cf9284b870d8", "key": "n8n-nodes-base.bubble", "subcategory": "*", "properties": {"displayName": "Bubble", "defaults": {"name": "Bubble"}, "description": "Consume the Bubble Data API", "name": "n8n-nodes-base.bubble", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Bubble/bubble.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Bubble/bubble.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.bubble/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/bubble/"}]}}}, "type": "node"}, {"uuid": "1cd41b24-b984-49ac-9f3a-4e6af782bd14", "key": "n8n-nodes-base.chargebee", "subcategory": "*", "properties": {"displayName": "Chargebee", "defaults": {"name": "Chargebee"}, "description": "Starts the workflow when Chargebee events occur", "name": "n8n-nodes-base.chargebee", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Chargebee/chargebee.png", "outputs": ["main"], "codex": {"categories": ["Finance & Accounting"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.chargebee/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/chargebee/"}]}}}, "type": "node"}, {"uuid": "093ae797-a842-4ba9-b3fe-cb29d095781c", "key": "n8n-nodes-base.circleCi", "subcategory": "*", "properties": {"displayName": "CircleCI", "defaults": {"name": "CircleCI"}, "description": "Consume CircleCI API", "name": "n8n-nodes-base.circleCi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/CircleCi/circleCi.svg", "dark": "icons/n8n-nodes-base/dist/nodes/CircleCi/circleCi.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.circleci/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/circleCi/"}]}}}, "type": "node"}, {"uuid": "67b19892-e1ee-4291-b5b1-19bf7191fabb", "key": "n8n-nodes-base.ciscoWebex", "subcategory": "*", "properties": {"displayName": "Webex by Cisco", "defaults": {"name": "Webex by Cisco"}, "description": "Starts the workflow when Cisco Webex events occur.", "name": "n8n-nodes-base.ciscoWebex", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Cisco/Webex/ciscoWebex.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.ciscowebex/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/ciscoWebex/"}]}}}, "type": "node"}, {"uuid": "9f55ce90-6909-4e99-b3f6-2b6f17e7031f", "key": "n8n-nodes-base.cloudflare", "subcategory": "*", "properties": {"displayName": "Cloudflare", "defaults": {"name": "Cloudflare"}, "description": "Consume Cloudflare API", "name": "n8n-nodes-base.cloudflare", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Cloudflare/cloudflare.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.cloudflare/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/cloudflare/"}]}}}, "type": "node"}, {"uuid": "201e99f1-6f22-49b8-a79d-9c523470d7f1", "key": "n8n-nodes-base.clearbit", "subcategory": "*", "properties": {"displayName": "Clearbit", "defaults": {"name": "Clearbit"}, "description": "Consume Clearbit API", "name": "n8n-nodes-base.clearbit", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Clearbit/clearbit.svg", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.clearbit/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/clearbit/"}]}}}, "type": "node"}, {"uuid": "51c8ad29-1092-4655-bc07-8ee23891783c", "key": "n8n-nodes-base.clickUp", "subcategory": "*", "properties": {"displayName": "ClickUp", "defaults": {"name": "ClickUp"}, "description": "Handle ClickUp events via webhooks (Beta)", "name": "n8n-nodes-base.clickUp", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ClickUp/clickup.svg", "outputs": ["main"], "codex": {"categories": ["Productivity", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.clickup/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/clickUp/"}]}}}, "type": "node"}, {"uuid": "4f565cb8-c44a-4b95-a65f-6be14f44055a", "key": "n8n-nodes-base.clockify", "subcategory": "*", "properties": {"displayName": "Clockify", "defaults": {"name": "Clockify"}, "description": "Listens to Clockify events", "name": "n8n-nodes-base.clockify", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Clockify/clockify.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Clockify/clockify.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.clockify/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/clockify/"}]}}}, "type": "node"}, {"uuid": "474cffdc-97f6-4a43-a15c-ef376ceeb0f8", "key": "n8n-nodes-base.cockpit", "subcategory": "*", "properties": {"displayName": "Cockpit", "defaults": {"name": "Cockpit"}, "description": "Consume Cockpit API", "name": "n8n-nodes-base.cockpit", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Cockpit/cockpit.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Cockpit/cockpit.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Marketing", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.cockpit/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/cockpit/"}]}}}, "type": "node"}, {"uuid": "18ed91e2-372a-4732-9b5d-99292a4ecbe8", "key": "n8n-nodes-base.coda", "subcategory": "*", "properties": {"displayName": "Coda", "defaults": {"name": "Coda"}, "description": "Consume Coda API", "name": "n8n-nodes-base.coda", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Coda/coda.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.coda/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/coda/"}]}}}, "type": "node"}, {"uuid": "************************************", "key": "n8n-nodes-base.code", "subcategory": "Helpers", "properties": {"displayName": "Code", "defaults": {"name": "Code"}, "description": "Run custom JavaScript or Python code", "name": "n8n-nodes-base.code", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Code/code.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers", "Data Transformation"]}, "alias": ["cpde", "Javascript", "JS", "Python", "<PERSON><PERSON><PERSON>", "Custom Code", "Function"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.code/"}]}}}, "type": "node"}, {"uuid": "a3082e52-3e0d-4462-8f2e-5e767ab7a36d", "key": "n8n-nodes-base.coinGecko", "subcategory": "*", "properties": {"displayName": "CoinGecko", "defaults": {"name": "CoinGecko"}, "description": "Consume CoinGecko API", "name": "n8n-nodes-base.coinGecko", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/CoinGecko/coinGecko.svg", "outputs": ["main"], "codex": {"categories": ["Productivity", "Finance & Accounting"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.coingecko/"}]}}}, "type": "node"}, {"uuid": "cb3870ac-b8a0-4702-9416-d0f494fe95c7", "key": "n8n-nodes-base.compareDatasets", "subcategory": "Flow", "properties": {"displayName": "Compare Datasets", "defaults": {"name": "Compare Datasets"}, "description": "Compare two inputs for changes", "name": "n8n-nodes-base.compareDatasets", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/CompareDatasets/compare.svg", "outputs": ["main", "main", "main", "main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Flow"]}, "alias": ["Join", "Concatenate", "Compare", "Dataset", "Split", "Sync", "Syncing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.comparedatasets/"}]}}}, "type": "node"}, {"uuid": "820fb9f0-b928-4d3f-bc83-fb3ccb66ecd6", "key": "n8n-nodes-base.compression", "subcategory": "Files", "properties": {"displayName": "Compression", "defaults": {"name": "Compression", "color": "#408000"}, "description": "Compress and decompress files", "name": "n8n-nodes-base.compression", "group": ["transform"], "icon": "fa:file-archive", "iconColor": "green", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>", "Data & Storage"], "subcategories": {"Core Nodes": ["Files", "Data Transformation"]}, "alias": ["Zip", "Gzip", "uncompress", "compress", "decompress", "archive", "unarchive", "Binary", "Files", "File"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.compression/"}]}}}, "type": "node"}, {"uuid": "424a5762-f31f-4759-b104-1e7a949b8ca1", "key": "n8n-nodes-base.contentful", "subcategory": "*", "properties": {"displayName": "Contentful", "defaults": {"name": "Contentful"}, "description": "Consume Contentful API", "name": "n8n-nodes-base.contentful", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Contentful/contentful.png", "outputs": ["main"], "codex": {"categories": ["Marketing", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.contentful/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/contentful/"}]}}}, "type": "node"}, {"uuid": "96c8b143-0613-4829-8f09-bae6b628e399", "key": "n8n-nodes-base.convertKit", "subcategory": "*", "properties": {"displayName": "ConvertKit", "defaults": {"name": "ConvertKit"}, "description": "Handle ConvertKit events via webhooks", "name": "n8n-nodes-base.convertKit", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ConvertKit/convertKit.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.convertkit/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/convertKit/"}]}}}, "type": "node"}, {"uuid": "ab6fa1be-f20d-4724-bf22-9d75a10ae6cd", "key": "n8n-nodes-base.copper", "subcategory": "*", "properties": {"displayName": "Copper", "defaults": {"name": "Copper"}, "description": "Handle Copper events via webhooks", "name": "n8n-nodes-base.copper", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Copper/copper.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.copper/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/copper/"}]}}}, "type": "node"}, {"uuid": "1c52c0e0-fe78-4dc6-9c55-3784b4a85447", "key": "n8n-nodes-base.cortex", "subcategory": "*", "properties": {"displayName": "<PERSON>rtex", "defaults": {"name": "<PERSON>rtex"}, "description": "Apply the Cortex analyzer/responder on the given entity", "name": "n8n-nodes-base.cortex", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Cortex/cortex.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Analytics"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.cortex/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/cortex/"}]}}}, "type": "node"}, {"uuid": "d7951ba4-179b-4887-ac2b-52db52bb2be5", "key": "n8n-nodes-base.crateDb", "subcategory": "*", "properties": {"displayName": "CrateDB", "defaults": {"name": "CrateDB"}, "description": "Add and update data in CrateDB", "name": "n8n-nodes-base.crateDb", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/CrateDb/cratedb.png", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.cratedb/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/crateDb/"}]}}}, "type": "node"}, {"uuid": "17931f57-fd44-46d9-90b2-ed18ec2c9e5b", "key": "n8n-nodes-base.crowdDev", "subcategory": "*", "properties": {"displayName": "crowd.dev", "defaults": {"name": "crowd.dev"}, "description": "Starts the workflow when crowd.dev events occur.", "name": "n8n-nodes-base.crowdDev", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/CrowdDev/crowdDev.svg", "dark": "icons/n8n-nodes-base/dist/nodes/CrowdDev/crowdDev.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.crowddev/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/crowddev/"}]}}}, "type": "node"}, {"uuid": "886944bc-0e59-4254-a657-477410bd658f", "key": "n8n-nodes-base.crypto", "subcategory": "Data Transformation", "properties": {"displayName": "Crypto", "defaults": {"name": "Crypto", "color": "#408000"}, "description": "Provide cryptographic utilities", "name": "n8n-nodes-base.crypto", "group": ["transform"], "icon": "fa:key", "iconColor": "green", "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["Encrypt", "SHA", "Hash"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.crypto/"}]}}}, "type": "node"}, {"uuid": "3839e2ba-c4f2-4bb1-87ea-f31817a784e8", "key": "n8n-nodes-base.customerIo", "subcategory": "*", "properties": {"displayName": "Customer.io", "defaults": {"name": "Customer.io"}, "description": "Starts the workflow on a Customer.io update (Beta)", "name": "n8n-nodes-base.customerIo", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/CustomerIo/customerio.svg", "dark": "icons/n8n-nodes-base/dist/nodes/CustomerIo/customerio.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.customerio/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/customerIo/"}]}}}, "type": "node"}, {"uuid": "22683f0a-d4be-4d55-bd96-a1895847a90c", "key": "n8n-nodes-base.dateTime", "subcategory": "Data Transformation", "properties": {"displayName": "Date & Time", "defaults": {"name": "Date & Time", "color": "#408000"}, "description": "Manipulate date and time values", "name": "n8n-nodes-base.dateTime", "group": ["transform"], "icon": "fa:clock", "iconColor": "green", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.datetime/"}]}}}, "type": "node"}, {"uuid": "4c0c1276-e569-48f3-b4db-5ed9fc5d62d9", "key": "n8n-nodes-base.debugHelper", "subcategory": "*", "properties": {"displayName": "DebugHelper", "defaults": {"name": "DebugHelper"}, "description": "Causes problems intentionally and generates useful data for debugging", "name": "n8n-nodes-base.debugHelper", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/DebugHelper/DebugHelper.svg", "dark": "icons/n8n-nodes-base/dist/nodes/DebugHelper/DebugHelper.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "alias": ["<PERSON><PERSON>", "<PERSON><PERSON>", "Demo", "Test", "Throw error", "OOM", "Out of Memory", "placeholder"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.debughelper/"}]}}}, "type": "node"}, {"uuid": "c8870a37-43f5-4b8d-b83a-480cc38160a9", "key": "n8n-nodes-base.deepL", "subcategory": "*", "properties": {"displayName": "DeepL", "defaults": {"name": "DeepL"}, "description": "Translate data using DeepL", "name": "n8n-nodes-base.deepL", "group": ["input", "output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/DeepL/deepl.svg", "dark": "icons/n8n-nodes-base/dist/nodes/DeepL/deepL.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Utility"], "alias": ["Translate", "Translator"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.deepl/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/deepL/"}]}}}, "type": "node"}, {"uuid": "65f80322-392b-4250-872e-42e23e312f80", "key": "n8n-nodes-base.demio", "subcategory": "*", "properties": {"displayName": "De<PERSON>", "defaults": {"name": "De<PERSON>"}, "description": "Consume the Demio API", "name": "n8n-nodes-base.demio", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Demio/demio.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.demio/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/demio/"}]}}}, "type": "node"}, {"uuid": "47d77489-6850-4aa3-9399-36388476b77d", "key": "n8n-nodes-base.dhl", "subcategory": "*", "properties": {"displayName": "DHL", "defaults": {"name": "DHL"}, "description": "Consume DHL API", "name": "n8n-nodes-base.dhl", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Dhl/dhl.svg", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "alias": ["Shipping"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.dhl/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/dhl/"}]}}}, "type": "node"}, {"uuid": "b7dcf5d3-e4a1-4185-88c1-b23acb6f92a2", "key": "n8n-nodes-base.discord", "subcategory": "*", "properties": {"displayName": "Discord", "defaults": {"name": "Discord"}, "description": "Sends data to Discord", "name": "n8n-nodes-base.discord", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Discord/discord.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL"], "subcategories": {"HITL": ["Human in the Loop"]}, "alias": ["human", "form", "wait", "hitl", "approval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.discord/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/discord/"}]}}}, "type": "node"}, {"uuid": "815be1fb-0dba-4b12-b15c-5c5d25a3ee94", "key": "n8n-nodes-base.discourse", "subcategory": "*", "properties": {"displayName": "Discourse", "defaults": {"name": "Discourse"}, "description": "Consume Discourse API", "name": "n8n-nodes-base.discourse", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Discourse/discourse.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.discourse/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/discourse/"}]}}}, "type": "node"}, {"uuid": "8de5d176-5b1e-41b5-b282-6c77dc7bf57e", "key": "n8n-nodes-base.disqus", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Access data on Disqus", "name": "n8n-nodes-base.disqus", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Disqus/disqus.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.disqus/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/disqus/"}]}}}, "type": "node"}, {"uuid": "f8525b66-cedb-4b79-aa5b-84752fd10779", "key": "n8n-nodes-base.drift", "subcategory": "*", "properties": {"displayName": "Drift", "defaults": {"name": "Drift"}, "description": "Consume Drift API", "name": "n8n-nodes-base.drift", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Drift/drift.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Drift/drift.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.drift/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/drift/"}]}}}, "type": "node"}, {"uuid": "9fe2fafe-446b-43fb-bd6d-9adcb0814a42", "key": "n8n-nodes-base.dropbox", "subcategory": "*", "properties": {"displayName": "Dropbox", "defaults": {"name": "Dropbox"}, "description": "Access data on Dropbox", "name": "n8n-nodes-base.dropbox", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Dropbox/dropbox.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.dropbox/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/dropbox/"}]}}}, "type": "node"}, {"uuid": "bce8b3a9-29ee-44cc-9736-3bc31bd4e947", "key": "n8n-nodes-base.dropcontact", "subcategory": "*", "properties": {"displayName": "Dropcontact", "defaults": {"name": "Dropcontact"}, "description": "Find B2B emails and enrich contacts", "name": "n8n-nodes-base.dropcontact", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Dropcontact/dropcontact.svg", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.dropcontact/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/dropcontact/"}]}}}, "type": "node"}, {"uuid": "ad91a425-a58d-4640-91bb-9edb68421cb3", "key": "n8n-nodes-base.editImage", "subcategory": "Files", "properties": {"displayName": "Edit Image", "defaults": {"name": "Edit Image", "color": "#553399"}, "description": "Edits an image like blur, resize or adding border and text", "name": "n8n-nodes-base.editImage", "group": ["transform"], "icon": "fa:image", "iconColor": "purple", "outputs": ["main"], "codex": {"categories": ["Marketing", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Files", "Data Transformation"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.editimage/"}]}}}, "type": "node"}, {"uuid": "0205a9b0-a6fa-4ecf-b5ca-3783d6fafdba", "key": "n8n-nodes-base.egoi", "subcategory": "*", "properties": {"displayName": "E-goi", "defaults": {"name": "E-goi"}, "description": "Consume E-goi API", "name": "n8n-nodes-base.egoi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Egoi/egoi.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.egoi/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/egoi/"}]}}}, "type": "node"}, {"uuid": "6ee37c6b-e29c-4336-9c97-5b901f601c9b", "key": "n8n-nodes-base.elasticsearch", "subcategory": "*", "properties": {"displayName": "Elasticsearch", "defaults": {"name": "Elasticsearch"}, "description": "Consume the Elasticsearch API", "name": "n8n-nodes-base.elasticsearch", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Elastic/Elasticsearch/elasticsearch.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.elasticsearch/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/elasticsearch/"}]}}}, "type": "node"}, {"uuid": "5bf92b4e-7c0a-478b-8628-12e84ddb414e", "key": "n8n-nodes-base.elasticSecurity", "subcategory": "*", "properties": {"displayName": "Elastic Security", "defaults": {"name": "Elastic Security"}, "description": "Consume the Elastic Security API", "name": "n8n-nodes-base.elasticSecurity", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Elastic/ElasticSecurity/elasticSecurity.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.elasticsecurity/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/elasticSecurity/"}]}}}, "type": "node"}, {"uuid": "e7c23aca-069d-4a97-9791-e2efe93b2543", "key": "n8n-nodes-base.emailSend", "subcategory": "*", "properties": {"displayName": "Send Email", "defaults": {"name": "Send Email", "color": "#00bb88"}, "description": "Sends an email using SMTP protocol", "name": "n8n-nodes-base.emailSend", "group": ["output"], "icon": "fa:envelope", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL", "Core <PERSON>"], "subcategories": {"HITL": ["Human in the Loop"]}, "alias": ["SMTP", "email", "human", "form", "wait", "hitl", "approval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.sendemail/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/credentials/sendemail/"}]}}}, "type": "node"}, {"uuid": "1c78a76b-090b-4b82-8bf5-3a6cd1d25702", "key": "n8n-nodes-base.emelia", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Handle Emelia campaign activity events via webhooks", "name": "n8n-nodes-base.emelia", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Emelia/emelia.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.emelia/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/emelia/"}]}}}, "type": "node"}, {"uuid": "b1e2e4d5-ed6e-4dee-a7b7-1ff70b0cba52", "key": "n8n-nodes-base.erpNext", "subcategory": "*", "properties": {"displayName": "ERPNext", "defaults": {"name": "ERPNext"}, "description": "Consume ERPNext API", "name": "n8n-nodes-base.erpNext", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ERPNext/erpnext.svg", "outputs": ["main"], "codex": {"categories": ["Finance & Accounting", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.erpnext/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/erpnext/"}]}}}, "type": "node"}, {"uuid": "6ad334b7-d34b-4f19-bdd4-86dc85a93ed9", "key": "n8n-nodes-base.executeCommand", "subcategory": "Helpers", "properties": {"displayName": "Execute Command", "defaults": {"name": "Execute Command", "color": "#886644"}, "description": "Executes a command on the host", "name": "n8n-nodes-base.executeCommand", "group": ["transform"], "icon": "fa:terminal", "iconColor": "crimson", "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers"]}, "alias": ["Shell", "Command", "OS", "<PERSON><PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executecommand/"}]}}}, "type": "node"}, {"uuid": "4849c078-8128-4b2f-9d2a-8d6c3f1ce482", "key": "n8n-nodes-base.executeWorkflow", "subcategory": "Helpers", "properties": {"displayName": "Execute Sub-workflow", "defaults": {"name": "Execute Workflow", "color": "#ff6d5a"}, "description": "Helpers for calling other n8n workflows. Used for designing modular, microservice-like workflows.", "name": "n8n-nodes-base.executeWorkflow", "group": ["transform"], "icon": "fa:sign-in-alt", "iconColor": "orange-red", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers", "Flow"]}, "alias": ["n8n", "call", "sub", "workflow", "sub-workflow", "subworkflow"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executeworkflow/"}]}}}, "type": "node"}, {"uuid": "8f4f1bcc-6b6a-482d-b6bb-d83c3de2859b", "key": "n8n-nodes-base.executionData", "subcategory": "Helpers", "properties": {"displayName": "Execution Data", "defaults": {"name": "Execution Data", "color": "#29A568"}, "description": "Add execution data for search", "name": "n8n-nodes-base.executionData", "group": ["input"], "icon": "fa:tasks", "iconColor": "light-green", "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers"]}, "alias": ["Filter", "_Set", "Data"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.executiondata/"}]}}}, "type": "node"}, {"uuid": "197c4ab6-b25e-4017-a586-b6e9168da78a", "key": "n8n-nodes-base.facebookGraphApi", "subcategory": "*", "properties": {"displayName": "Facebook Graph API", "defaults": {"name": "Facebook Graph API"}, "description": "Interacts with Facebook using the Graph API", "name": "n8n-nodes-base.facebookGraphApi", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Facebook/facebook.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.facebookgraphapi/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/facebookGraph/"}]}}}, "type": "node"}, {"uuid": "d400d743-81ab-40dd-a432-53b353962a77", "key": "n8n-nodes-base.filemaker", "subcategory": "*", "properties": {"displayName": "FileMaker", "defaults": {"name": "FileMaker"}, "description": "Retrieve data from the FileMaker data API", "name": "n8n-nodes-base.filemaker", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/FileMaker/filemaker.png", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.filemaker/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/fileMaker/"}]}}}, "type": "node"}, {"uuid": "8a0ac3ff-6d36-4068-901d-f42f460b4f57", "key": "n8n-nodes-base.readWriteFile", "subcategory": "Files", "properties": {"displayName": "Read/Write Files from Disk", "defaults": {"name": "Read/Write Files from Disk"}, "description": "Read or write files from the computer that runs n8n", "name": "n8n-nodes-base.readWriteFile", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Files/ReadWriteFile/readWriteFile.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Files"]}, "alias": ["Binary", "File", "Text", "Open", "Import", "Save", "Export", "Disk", "Transfer"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.readwritefile/"}]}}}, "type": "node"}, {"uuid": "b2a89dc9-57c3-428c-bca7-fec812b26000", "key": "n8n-nodes-base.convertToFile", "subcategory": "Files", "properties": {"displayName": "Convert to File", "defaults": {"name": "Convert to File"}, "description": "Convert JSON data to binary data", "name": "n8n-nodes-base.convertToFile", "group": ["input"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Files/ConvertToFile/convertToFile.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Files/ConvertToFile/convertToFile.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Files", "Data Transformation"]}, "alias": ["CSV", "Spreadsheet", "Excel", "xls", "xlsx", "ods", "tabular", "encode", "encoding", "Move Binary Data", "Binary", "File", "JSON", "HTML", "ICS", "iCal", "RTF", "64", "Base64"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.converttofile/"}]}}}, "type": "node"}, {"uuid": "77e40644-2b6b-4e15-9385-51c20e10b4ef", "key": "n8n-nodes-base.extractFromFile", "subcategory": "Files", "properties": {"displayName": "Extract from File", "defaults": {"name": "Extract from File"}, "description": "Convert binary data to JSON", "name": "n8n-nodes-base.extractFromFile", "group": ["input"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Files/ExtractFromFile/extractFromFile.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Files/ExtractFromFile/extractFromFile.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Files", "Data Transformation"]}, "alias": ["CSV", "Spreadsheet", "Excel", "xls", "xlsx", "ods", "tabular", "decode", "decoding", "Move Binary Data", "Binary", "File", "PDF", "JSON", "HTML", "ICS", "iCal", "txt", "Text", "RTF", "XML", "64", "Base64", "Convert"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.extractfromfile/"}]}}}, "type": "node"}, {"uuid": "472b0230-0dca-4864-876f-46030decf8a6", "key": "n8n-nodes-base.filter", "subcategory": "Flow", "properties": {"displayName": "Filter", "defaults": {"name": "Filter", "color": "#229eff"}, "description": "Remove items matching a condition", "name": "n8n-nodes-base.filter", "group": ["transform"], "icon": "fa:filter", "iconColor": "light-blue", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Flow", "Data Transformation"]}, "alias": ["Router", "Filter", "Condition", "Logic", "Boolean", "Branch"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.filter/"}]}}}, "type": "node"}, {"uuid": "1912553f-223a-4609-9326-32b08a3a7597", "key": "n8n-nodes-base.flow", "subcategory": "*", "properties": {"displayName": "Flow", "defaults": {"name": "Flow"}, "description": "Handle Flow events via webhooks", "name": "n8n-nodes-base.flow", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Flow/flow.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.flow/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/flow/"}]}}}, "type": "node"}, {"uuid": "aa266322-7880-4461-a30a-e57e29a42a9e", "key": "n8n-nodes-base.form", "subcategory": "Helpers", "properties": {"displayName": "n8n Form", "defaults": {"name": "Form"}, "description": "Generate webforms in n8n and pass their responses to the workflow", "name": "n8n-nodes-base.form", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Form/form.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers"]}, "alias": ["_Form", "form", "table", "submit", "post", "page", "step", "stage", "multi"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.form/"}]}}}, "type": "node"}, {"uuid": "7cbae154-04c4-445f-b8b3-0cb0afae078f", "key": "n8n-nodes-base.freshdesk", "subcategory": "*", "properties": {"displayName": "Freshdesk", "defaults": {"name": "Freshdesk"}, "description": "Consume Freshdesk API", "name": "n8n-nodes-base.freshdesk", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Freshdesk/freshdesk.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.freshdesk/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/freshdesk/"}]}}}, "type": "node"}, {"uuid": "0ebf691a-e975-4c2d-b02c-a1bf3363f67b", "key": "n8n-nodes-base.freshservice", "subcategory": "*", "properties": {"displayName": "Freshservice", "defaults": {"name": "Freshservice"}, "description": "Consume the Freshservice API", "name": "n8n-nodes-base.freshservice", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Freshservice/freshservice.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "alias": ["Freshdesk"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.freshservice/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/freshservice/"}]}}}, "type": "node"}, {"uuid": "1d5a6146-060f-46e2-8c57-6e33ca28e0e0", "key": "n8n-nodes-base.freshworksCrm", "subcategory": "*", "properties": {"displayName": "Freshworks CRM", "defaults": {"name": "Freshworks CRM"}, "description": "Consume the Freshworks CRM API", "name": "n8n-nodes-base.freshworksCrm", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/FreshworksCrm/freshworksCrm.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Sales"], "alias": ["Freshdesk"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.freshworkscrm/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/freshworksCrm/"}]}}}, "type": "node"}, {"uuid": "e22fdda2-ec69-4238-9fc6-a8471b2255f9", "key": "n8n-nodes-base.ftp", "subcategory": "Files", "properties": {"displayName": "FTP", "defaults": {"name": "FTP", "color": "#303050"}, "description": "Transfer files via FTP or SFTP", "name": "n8n-nodes-base.ftp", "group": ["input"], "icon": "fa:server", "iconColor": "dark-blue", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>", "Data & Storage", "Development", "Utility"], "subcategories": {"Core Nodes": ["Files", "Helpers"]}, "alias": ["SFTP", "FTP", "Binary", "File", "Transfer"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.ftp/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/ftp/"}]}}}, "type": "node"}, {"uuid": "8df701fc-b59a-4fc7-8253-7d7da3fa9974", "key": "n8n-nodes-base.getResponse", "subcategory": "*", "properties": {"displayName": "GetResponse", "defaults": {"name": "GetResponse"}, "description": "Starts the workflow when GetResponse events occur", "name": "n8n-nodes-base.getResponse", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/GetResponse/getResponse.png", "outputs": ["main"], "codex": {"categories": ["Communication", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.getresponse/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/getResponse/"}]}}}, "type": "node"}, {"uuid": "0baddd6f-e10f-4c3c-9ed8-50d22b3a0869", "key": "n8n-nodes-base.ghost", "subcategory": "*", "properties": {"displayName": "Ghost", "defaults": {"name": "Ghost"}, "description": "Consume Ghost API", "name": "n8n-nodes-base.ghost", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Ghost/ghost.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.ghost/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/ghost/"}]}}}, "type": "node"}, {"uuid": "770d8228-7ec4-4c57-a41c-c4c4d062dd12", "key": "n8n-nodes-base.git", "subcategory": "*", "properties": {"displayName": "Git", "defaults": {"name": "Git"}, "description": "Control git.", "name": "n8n-nodes-base.git", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Git/git.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>", "Development"], "subcategories": ["Helpers"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.git/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/git/"}]}}}, "type": "node"}, {"uuid": "988c5539-00c6-44f2-b730-905180fbe9ae", "key": "n8n-nodes-base.github", "subcategory": "*", "properties": {"displayName": "GitHub", "defaults": {"name": "GitHub"}, "description": "Starts the workflow when Github events occur", "name": "n8n-nodes-base.github", "group": ["input"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Github/github.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Github/github.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.github/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/github/"}]}}}, "type": "node"}, {"uuid": "a014dd69-30d0-4647-9d76-93785638570e", "key": "n8n-nodes-base.gitlab", "subcategory": "*", "properties": {"displayName": "GitLab", "defaults": {"name": "GitLab"}, "description": "Starts the workflow when GitLab events occur", "name": "n8n-nodes-base.gitlab", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gitlab/gitlab.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gitlab/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/gitlab/"}]}}}, "type": "node"}, {"uuid": "f78f81e7-659f-48c2-9c5f-f8fd690aff01", "key": "n8n-nodes-base.gong", "subcategory": "*", "properties": {"displayName": "<PERSON>", "defaults": {"name": "<PERSON>"}, "description": "Interact with Gong API", "name": "n8n-nodes-base.gong", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gong/gong.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Developer Tools"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gong/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gong/"}]}}}, "type": "node"}, {"uuid": "dacd4b28-d7c0-4a20-b829-d47c2aeec49d", "key": "n8n-nodes-base.googleAds", "subcategory": "*", "properties": {"displayName": "Google Ads", "defaults": {"name": "Google Ads"}, "description": "Use the Google Ads API", "name": "n8n-nodes-base.googleAds", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Ads/googleAds.svg", "outputs": ["main"], "codex": {"categories": ["Analytics"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googleads/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "722d60f8-4593-40bb-96d6-8a076c523d54", "key": "n8n-nodes-base.googleAnalytics", "subcategory": "*", "properties": {"displayName": "Google Analytics", "defaults": {"name": "Google Analytics"}, "description": "Use the Google Analytics API", "name": "n8n-nodes-base.googleAnalytics", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Analytics/analytics.svg", "outputs": ["main"], "codex": {"categories": ["Analytics"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googleanalytics/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "14e5323a-e67a-407c-8b4c-8ef76f68af10", "key": "n8n-nodes-base.googleBigQuery", "subcategory": "*", "properties": {"displayName": "Google BigQuery", "defaults": {"name": "Google BigQuery"}, "description": "Consume Google BigQuery API", "name": "n8n-nodes-base.googleBigQuery", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/BigQuery/googleBigQuery.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlebigquery/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "d30fcc27-7881-47fd-8f83-e4f1652d5594", "key": "n8n-nodes-base.googleBooks", "subcategory": "*", "properties": {"displayName": "Google Books", "defaults": {"name": "Google Books"}, "description": "Read data from Google Books", "name": "n8n-nodes-base.googleBooks", "group": ["input", "output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Books/googlebooks.svg", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlebooks/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "1cf33907-7498-4c34-8a9e-851b487dd9c4", "key": "n8n-nodes-base.googleCalendar", "subcategory": "*", "properties": {"displayName": "Google Calendar", "defaults": {"name": "Google Calendar"}, "description": "Starts the workflow when Google Calendar events occur", "name": "n8n-nodes-base.googleCalendar", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Calendar/googleCalendar.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlecalendar/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "bf2c7c55-937a-460b-a22e-c3d3859b37a1", "key": "n8n-nodes-base.googleChat", "subcategory": "*", "properties": {"displayName": "Google Chat", "defaults": {"name": "Google Chat"}, "description": "Consume Google Chat API", "name": "n8n-nodes-base.googleChat", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Chat/googleChat.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL"], "subcategories": {"HITL": ["Human in the Loop"]}, "alias": ["human", "form", "wait", "hitl", "approval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlechat/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/service-account/"}]}}}, "type": "node"}, {"uuid": "e607ded0-1643-4785-bb57-4080711b3a07", "key": "n8n-nodes-base.googleCloudNaturalLanguage", "subcategory": "*", "properties": {"displayName": "Google Cloud Natural Language", "defaults": {"name": "Google Cloud Natural Language"}, "description": "Consume Google Cloud Natural Language API", "name": "n8n-nodes-base.googleCloudNaturalLanguage", "group": ["input", "output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/CloudNaturalLanguage/googlecloudnaturallanguage.png", "outputs": ["main"], "codex": {"categories": ["Analytics"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlecloudnaturallanguage/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "cc636ba1-2ce2-4247-b1d3-394bb0efda7f", "key": "n8n-nodes-base.googleCloudStorage", "subcategory": "*", "properties": {"displayName": "Google Cloud Storage", "defaults": {"name": "Google Cloud Storage"}, "description": "Use the Google Cloud Storage API", "name": "n8n-nodes-base.googleCloudStorage", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/CloudStorage/googleCloudStorage.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlecloudstorage/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "973bab8c-a2ac-414b-be48-73446ecfd1c8", "key": "n8n-nodes-base.googleContacts", "subcategory": "*", "properties": {"displayName": "Google Contacts", "defaults": {"name": "Google Contacts"}, "description": "Consume Google Contacts API", "name": "n8n-nodes-base.googleContacts", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Contacts/googleContacts.png", "outputs": ["main"], "codex": {"categories": ["Miscellaneous "], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlecontacts/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "883c2bfa-7a02-40b9-9d64-eeeac6771e2c", "key": "n8n-nodes-base.googleDocs", "subcategory": "*", "properties": {"displayName": "Google Docs", "defaults": {"name": "Google Docs"}, "description": "Consume Google Docs API.", "name": "n8n-nodes-base.googleDocs", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Docs/googleDocs.svg", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledocs/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "9037d7f6-cb98-42b3-962b-25a7fd7a04b2", "key": "n8n-nodes-base.googleDrive", "subcategory": "*", "properties": {"displayName": "Google Drive", "defaults": {"name": "Google Drive"}, "description": "Starts the workflow when Google Drive events occur", "name": "n8n-nodes-base.googleDrive", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Drive/googleDrive.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googledrive/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "1c639cd8-f920-4615-9df8-34d499983fe9", "key": "n8n-nodes-base.googleFirebaseCloudFirestore", "subcategory": "*", "properties": {"displayName": "Google Cloud Firestore", "defaults": {"name": "Google Cloud Firestore"}, "description": "Interact with Google Firebase - Cloud Firestore API", "name": "n8n-nodes-base.googleFirebaseCloudFirestore", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Firebase/CloudFirestore/googleFirebaseCloudFirestore.png", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlecloudfirestore/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "dd682506-2a5f-48af-8ed7-dfcb9a1e50e0", "key": "n8n-nodes-base.googleFirebaseRealtimeDatabase", "subcategory": "*", "properties": {"displayName": "Google Cloud Realtime Database", "defaults": {"name": "Google Cloud Realtime Database"}, "description": "Interact with Google Firebase - Realtime Database API", "name": "n8n-nodes-base.googleFirebaseRealtimeDatabase", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Firebase/RealtimeDatabase/googleFirebaseRealtimeDatabase.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlecloudrealtimedatabase/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "538dcc72-7a22-4741-94d6-54fc49845b38", "key": "n8n-nodes-base.gmail", "subcategory": "*", "properties": {"displayName": "Gmail", "defaults": {"name": "Gmail"}, "description": "Fetches emails from Gmail and starts the workflow on specified polling intervals.", "name": "n8n-nodes-base.gmail", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Gmail/gmail.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL"], "subcategories": {"HITL": ["Human in the Loop"]}, "alias": ["email", "human", "form", "wait", "hitl", "approval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gmail/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "bc61557a-a261-4b61-b781-5429b6671512", "key": "n8n-nodes-base.gSuiteAdmin", "subcategory": "*", "properties": {"displayName": "Google Workspace Admin", "defaults": {"name": "Google Workspace Admin"}, "description": "Consume Google Workspace Admin API", "name": "n8n-nodes-base.gSuiteAdmin", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/GSuiteAdmin/google-workspace-admin.png", "outputs": ["main"], "codex": {"categories": ["Utility"], "alias": ["Workspaces"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gsuiteadmin/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "2fd3a2d6-0efc-4a05-9e8c-253407a6b143", "key": "n8n-nodes-base.googleBusinessProfile", "subcategory": "*", "properties": {"displayName": "Google Business Profile", "defaults": {"name": "Google Business Profile"}, "description": "Fetches reviews from Google Business Profile and starts the workflow on specified polling intervals.", "name": "n8n-nodes-base.googleBusinessProfile", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/BusinessProfile/googleBusinessProfile.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Productivity"], "alias": ["Google My Business", "GMB", "My Business"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlebusinessprofile/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "5bfa298c-08f4-4411-85b0-0cde1cdf9b6d", "key": "n8n-nodes-base.googlePerspective", "subcategory": "*", "properties": {"displayName": "Google Perspective", "defaults": {"name": "Google Perspective"}, "description": "Consume Google Perspective API", "name": "n8n-nodes-base.googlePerspective", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Google/Perspective/googlePerspective.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Google/Perspective/googlePerspective.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Analytics", "Utility"], "alias": ["Moderation"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googleperspective/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "fea8b6c5-c005-4354-b2d7-364386ffb483", "key": "n8n-nodes-base.googleSheets", "subcategory": "*", "properties": {"displayName": "Google Sheets", "defaults": {"name": "Google Sheets"}, "description": "Starts the workflow when Google Sheets events occur", "name": "n8n-nodes-base.googleSheets", "group": ["input", "output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Sheet/googleSheets.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Productivity"], "alias": ["CSV", "Sheet", "Spreadsheet", "GS"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googlesheets/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "0a0764b5-5ca4-45dc-bcc0-1ec69b17fee4", "key": "n8n-nodes-base.googleSlides", "subcategory": "*", "properties": {"displayName": "Google Slides", "defaults": {"name": "Google Slides"}, "description": "Consume the Google Slides API", "name": "n8n-nodes-base.googleSlides", "group": ["input", "output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Slides/googleslides.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googleslides/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "c53b58c2-91d1-4737-8942-85515bf6b754", "key": "n8n-nodes-base.googleTasks", "subcategory": "*", "properties": {"displayName": "Google Tasks", "defaults": {"name": "Google Tasks"}, "description": "Consume Google Tasks API", "name": "n8n-nodes-base.googleTasks", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Task/googleTasks.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googletasks/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "d057a5b7-d465-429f-9b2d-82eba067f60a", "key": "n8n-nodes-base.googleTranslate", "subcategory": "*", "properties": {"displayName": "Google Translate", "defaults": {"name": "Google Translate"}, "description": "Translate data using Google Translate", "name": "n8n-nodes-base.googleTranslate", "group": ["input", "output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/Translate/googletranslate.png", "outputs": ["main"], "codex": {"categories": ["Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googletranslate/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "f30884f4-401a-489f-9243-c6e421fcb69c", "key": "n8n-nodes-base.youTube", "subcategory": "*", "properties": {"displayName": "YouTube", "defaults": {"name": "YouTube"}, "description": "Consume YouTube API", "name": "n8n-nodes-base.youTube", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Google/YouTube/youTube.png", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.youtube/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}]}}}, "type": "node"}, {"uuid": "c7f0b77a-dbb3-4d6d-b279-c975d7b2eaa7", "key": "n8n-nodes-base.gotify", "subcategory": "*", "properties": {"displayName": "Gotify", "defaults": {"name": "Gotify"}, "description": "Consume Gotify API", "name": "n8n-nodes-base.gotify", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gotify/gotify.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gotify/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/gotify/"}]}}}, "type": "node"}, {"uuid": "ce88fb48-7a0c-41bf-9f24-f06189143485", "key": "n8n-nodes-base.goToWebinar", "subcategory": "*", "properties": {"displayName": "GoToWebinar", "defaults": {"name": "GoToWebinar"}, "description": "Consume the GoToWebinar API", "name": "n8n-nodes-base.goToWebinar", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/GoToWebinar/gotowebinar.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.gotowebinar/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/goToWebinar/"}]}}}, "type": "node"}, {"uuid": "6941ab36-a33e-4611-9984-22740bbaa9b0", "key": "n8n-nodes-base.grafana", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>"}, "description": "Consume the Grafana API", "name": "n8n-nodes-base.grafana", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Grafana/grafana.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Analytics"], "alias": ["Prometheus"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.grafana/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/grafana/"}]}}}, "type": "node"}, {"uuid": "ba1f5692-a07c-465f-ab10-80c48ba11d65", "key": "n8n-nodes-base.graphql", "subcategory": "*", "properties": {"displayName": "GraphQL", "defaults": {"name": "GraphQL"}, "description": "Makes a GraphQL request and returns the received data", "name": "n8n-nodes-base.graphql", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/GraphQL/graphql.png", "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.graphql/"}]}}}, "type": "node"}, {"uuid": "d22deae2-2473-47d0-bb0d-2b043f9ed193", "key": "n8n-nodes-base.grist", "subcategory": "*", "properties": {"displayName": "Grist", "defaults": {"name": "Grist"}, "description": "Consume the Grist API", "name": "n8n-nodes-base.grist", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Grist/grist.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.grist/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/grist/"}]}}}, "type": "node"}, {"uuid": "eca239d9-8085-4799-918c-915b24b0837d", "key": "n8n-nodes-base.hackerNews", "subcategory": "*", "properties": {"displayName": "Hacker News", "defaults": {"name": "Hacker News"}, "description": "Consume Hacker News API", "name": "n8n-nodes-base.hackerNews", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HackerNews/hackernews.png", "outputs": ["main"], "codex": {"categories": ["Communication", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.hackernews/"}]}}}, "type": "node"}, {"uuid": "2e85231a-d93d-460a-a6d9-95f80c80d3b8", "key": "n8n-nodes-base.haloPSA", "subcategory": "*", "properties": {"displayName": "HaloPSA", "defaults": {"name": "HaloPSA"}, "description": "Consume HaloPSA API", "name": "n8n-nodes-base.haloPSA", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HaloPSA/halopsa.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.halopsa/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/haloPSA/"}]}}}, "type": "node"}, {"uuid": "07494745-c5a7-4679-b8c3-4ad185d5793e", "key": "n8n-nodes-base.harvest", "subcategory": "*", "properties": {"displayName": "Harvest", "defaults": {"name": "Harvest"}, "description": "Access data on Harvest", "name": "n8n-nodes-base.harvest", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Harvest/harvest.png", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.harvest/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/harvest/"}]}}}, "type": "node"}, {"uuid": "56712fae-825a-440d-a6da-7f264e94ba96", "key": "n8n-nodes-base.helpScout", "subcategory": "*", "properties": {"displayName": "HelpScout", "defaults": {"name": "HelpScout"}, "description": "Starts the workflow when HelpScout events occur", "name": "n8n-nodes-base.helpScout", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HelpScout/helpScout.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.helpscout/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/helpScout/"}]}}}, "type": "node"}, {"uuid": "17795b4d-d0b6-4fbb-8938-87eb9aff1a12", "key": "n8n-nodes-base.highLevel", "subcategory": "*", "properties": {"displayName": "HighLevel", "defaults": {"name": "HighLevel"}, "description": "Consume HighLevel API v2", "name": "n8n-nodes-base.highLevel", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HighLevel/highLevel.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.highlevel/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/highLevel/"}]}}}, "type": "node"}, {"uuid": "0d26a43d-039c-4a20-b90a-5e380633c320", "key": "n8n-nodes-base.homeAssistant", "subcategory": "*", "properties": {"displayName": "Home Assistant", "defaults": {"name": "Home Assistant"}, "description": "Consume Home Assistant API", "name": "n8n-nodes-base.homeAssistant", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HomeAssistant/homeAssistant.svg", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.homeassistant/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/homeAssistant/"}]}}}, "type": "node"}, {"uuid": "fa1057d0-7941-46be-8660-6809c1c20d5e", "key": "n8n-nodes-base.html", "subcategory": "Data Transformation", "properties": {"displayName": "HTML", "defaults": {"name": "HTML"}, "description": "Work with HTML", "name": "n8n-nodes-base.html", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Html/html.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Html/html.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["extract", "template", "table"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.html/"}]}}}, "type": "node"}, {"uuid": "447f356c-af3a-4b13-99cc-26addd84493f", "key": "n8n-creds-base.alienVaultApi", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "AlienVault HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.alienVaultApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/AlienVault.png", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "6a922918-eca8-45fb-9a41-af94a0611b92", "key": "n8n-creds-base.auth0ManagementApi", "subcategory": "*", "properties": {"displayName": "Auth0", "defaults": {"name": "Auth0 HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.auth0ManagementApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Auth0.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Auth0.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "eca7c78e-a625-48c3-926f-be14602f9766", "key": "n8n-creds-base.carbonBlackApi", "subcategory": "*", "properties": {"displayName": "Carbon Black", "defaults": {"name": "Carbon Black HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.carbonBlackApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/vmware.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/vmware.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "dd103ae6-b971-41e9-a036-5973fdc51507", "key": "n8n-creds-base.ciscoMerakiApi", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "Cisco Meraki HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.ciscoMerakiApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "e85a2a51-7163-436c-ad6c-bf1158cc3fca", "key": "n8n-creds-base.ciscoSecureEndpointApi", "subcategory": "*", "properties": {"displayName": "Cisco Secure Endpoint", "defaults": {"name": "Cisco Secure Endpoint HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.ciscoSecureEndpointApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "68ac6bd6-901d-4638-9755-6556f04b11d8", "key": "n8n-creds-base.ciscoUmbrellaApi", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "Cisco Umbrella HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.ciscoUmbrellaApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Cisco.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "ec2b974c-88f0-4df2-80b9-6acc260d540a", "key": "n8n-creds-base.convertApi", "subcategory": "*", "properties": {"displayName": "ConvertAPI", "defaults": {"name": "ConvertAPI HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.convertApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/ConvertApi.png", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "0827fdf0-ff5e-48fd-866f-a6aa3b0c86e6", "key": "n8n-creds-base.crowdStrikeOAuth2Api", "subcategory": "*", "properties": {"displayName": "CrowdStrike", "defaults": {"name": "CrowdStrike HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.crowdStrikeOAuth2Api", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/CrowdStrike.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/CrowdStrike.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "1f364d84-9e7b-4e5b-94fb-ecc928081dd1", "key": "n8n-creds-base.datadogApi", "subcategory": "*", "properties": {"displayName": "Datadog", "defaults": {"name": "Datadog HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.datadogApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Datadog.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Datadog.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "c580cc59-27fa-4020-812e-1693752928fc", "key": "n8n-creds-base.dfirIrisApi", "subcategory": "*", "properties": {"displayName": "DFIR-IRIS", "defaults": {"name": "DFIR-IRIS HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.dfirIrisApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/DfirIris.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/DfirIris.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "e2a94aec-c88d-4895-9fbc-af122c1e6dea", "key": "n8n-creds-base.dynatraceApi", "subcategory": "*", "properties": {"displayName": "Dynatrace", "defaults": {"name": "Dynatrace HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.dynatraceApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Dynatrace.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Dynatrace.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "a8c161a9-4bd8-4418-9ca5-c05ac7ad19c5", "key": "n8n-creds-base.f5BigIpApi", "subcategory": "*", "properties": {"displayName": "F5 Big-IP", "defaults": {"name": "F5 Big-IP HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.f5BigIpApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/F5.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "60b07dbb-c2f6-44ea-94c3-2dcc976f0f89", "key": "n8n-creds-base.filescanApi", "subcategory": "*", "properties": {"displayName": "Filescan", "defaults": {"name": "Filescan HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.filescanApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Filescan.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Filescan.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "e7e3b7df-e439-43e3-8966-007ba6c3fd22", "key": "n8n-creds-base.fortiGateApi", "subcategory": "*", "properties": {"displayName": "Fortinet FortiGate", "defaults": {"name": "Fortinet FortiGate HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.fortiGateApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Fortinet.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "e6075797-0dc3-4edd-bea4-9885898dcb06", "key": "n8n-creds-base.hybridAnalysisApi", "subcategory": "*", "properties": {"displayName": "Hybrid Analysis", "defaults": {"name": "Hybrid Analysis HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.hybridAnalysisApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Hybrid.png", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "850485a1-df52-49f4-be61-b50c9fcebd98", "key": "n8n-creds-base.impervaWafApi", "subcategory": "*", "properties": {"displayName": "Imperva WAF", "defaults": {"name": "Imperva WAF HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.impervaWafApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Imperva.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Imperva.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "396ace8b-e663-4a43-a9e8-5034c6e4c6ae", "key": "n8n-creds-base.kibanaApi", "subcategory": "*", "properties": {"displayName": "Kibana", "defaults": {"name": "Kibana HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.kibanaApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Kibana.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "27dddb78-c81a-400e-8425-c832aafd692d", "key": "n8n-creds-base.malcoreApi", "subcategory": "*", "properties": {"displayName": "Malcore", "defaults": {"name": "Malcore HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.malcoreApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Malcore.png", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Malcore.png"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "71209b4b-429a-4060-a4cb-79d9a536739a", "key": "n8n-creds-base.microsoftAzureMonitorOAuth2Api", "subcategory": "*", "properties": {"displayName": "Microsoft Azure Monitor", "defaults": {"name": "Microsoft Azure Monitor HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.microsoftAzureMonitorOAuth2Api", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Microsoft.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "7808ca87-a376-4465-a08c-43ac8f02c045", "key": "n8n-creds-base.microsoftSharePointOAuth2Api", "subcategory": "*", "properties": {"displayName": "Microsoft SharePoint", "defaults": {"name": "Microsoft SharePoint HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.microsoftSharePointOAuth2Api", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/SharePoint.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/SharePoint.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "00791560-5f72-414d-9fe0-e04243eef4ec", "key": "n8n-creds-base.miroOAuth2Api", "subcategory": "*", "properties": {"displayName": "Mir<PERSON>", "defaults": {"name": "Miro HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.miroOAuth2Api", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Miro.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "a02c9d21-128f-4084-a061-f084ee55b9c7", "key": "n8n-creds-base.mistApi", "subcategory": "*", "properties": {"displayName": "Mist", "defaults": {"name": "Mist HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.mistApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Mist.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "141f8c1d-47c9-4ebc-9737-c34fadc3c011", "key": "n8n-creds-base.openCtiApi", "subcategory": "*", "properties": {"displayName": "OpenCTI", "defaults": {"name": "OpenCTI HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.openCtiApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/OpenCTI.png", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "588c1628-2c0c-4c5b-8de3-9da52d7d355d", "key": "n8n-creds-base.qRadarApi", "subcategory": "*", "properties": {"displayName": "QRadar", "defaults": {"name": "QRadar HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.qRadarApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/IBM.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/IBM.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "efac86a9-3be9-4423-b6e4-11aab33d1c93", "key": "n8n-creds-base.qualysApi", "subcategory": "*", "properties": {"displayName": "Qualys", "defaults": {"name": "Qualys HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.qualysApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Qualys.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "54e08b62-72f7-4ad1-8621-877ce142fbd4", "key": "n8n-creds-base.rapid7InsightVmApi", "subcategory": "*", "properties": {"displayName": "Rapid7 InsightVM", "defaults": {"name": "Rapid7 InsightVM HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.rapid7InsightVmApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Rapid7InsightVm.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Rapid7InsightVm.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "07f32746-a075-40c4-9366-3cc47f3a1ea9", "key": "n8n-creds-base.recordedFutureApi", "subcategory": "*", "properties": {"displayName": "Recorded Future", "defaults": {"name": "Recorded Future HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.recordedFutureApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/RecordedFuture.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/RecordedFuture.dark.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "89eca2f5-727e-452c-8ced-44e59d2f5aae", "key": "n8n-creds-base.sekoiaApi", "subcategory": "*", "properties": {"displayName": "Sekoia", "defaults": {"name": "Sekoia HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.sekoiaApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Sekoia.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "0a01742c-ba71-49a1-9776-f94b2016e71c", "key": "n8n-creds-base.shuffler<PERSON>pi", "subcategory": "*", "properties": {"displayName": "<PERSON>ffle<PERSON>", "defaults": {"name": "Shuffler HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.shuffler<PERSON>pi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Shuffler.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "d0eef0e6-4a06-47a4-bfcc-f99def6bc112", "key": "n8n-creds-base.solarWindsIpamApi", "subcategory": "*", "properties": {"displayName": "SolarWinds IPAM", "defaults": {"name": "SolarWinds IPAM HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.solarWindsIpamApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/SolarWindsIpam.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/SolarWindsIpam.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "28a43445-012a-452c-9184-ba419069801e", "key": "n8n-creds-base.solarWindsObservabilityApi", "subcategory": "*", "properties": {"displayName": "SolarWinds Observability", "defaults": {"name": "SolarWinds Observability HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.solarWindsObservabilityApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/SolarWindsObservability.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/SolarWindsObservability.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "44f6cae8-6370-464c-a106-6d6981198ef9", "key": "n8n-creds-base.sysdigApi", "subcategory": "*", "properties": {"displayName": "Sysdig", "defaults": {"name": "Sysdig HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.sysdigApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/credentials/icons/Sysdig.Black.svg", "dark": "icons/n8n-nodes-base/dist/credentials/icons/Sysdig.White.svg"}, "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "182428ac-93e3-492b-900c-c97066e46077", "key": "n8n-creds-base.trellixEpoApi", "subcategory": "*", "properties": {"displayName": "Trellix (McAfee) ePolicy Orchestrator", "defaults": {"name": "Trellix (McAfee) ePolicy Orchestrator HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.trellixEpoApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Trellix.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "8332d946-f7e0-44f9-9378-1309e5d30ade", "key": "n8n-creds-base.twakeServerApi", "subcategory": "*", "properties": {"displayName": "Twake Server", "defaults": {"name": "Twake Server HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.twakeServerApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Twake.png", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "e91f5f48-4427-4b10-9fd0-614bf1b599d7", "key": "n8n-creds-base.verticaApi", "subcategory": "*", "properties": {"displayName": "Vertica", "defaults": {"name": "Vertica HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.verticaApi", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "9e1431dd-2656-4b0d-87b5-b9c7df28d0a0", "key": "n8n-creds-base.virusTotalApi", "subcategory": "*", "properties": {"displayName": "VirusTotal", "defaults": {"name": "VirusTotal HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.virusTotalApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/VirusTotal.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "ae7331be-298e-4576-9689-4ae68353d8ee", "key": "n8n-creds-base.zabbixApi", "subcategory": "*", "properties": {"displayName": "Zabbix", "defaults": {"name": "Zabbix HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.zabbixApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Zabbix.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "bb73a470-ffb6-4005-967a-edd51eabb212", "key": "n8n-creds-base.zscalerZiaApi", "subcategory": "*", "properties": {"displayName": "Zscaler ZIA", "defaults": {"name": "Zscaler ZIA HTTP Request", "color": "#0004F5"}, "description": "HTTP request", "name": "n8n-creds-base.zscalerZiaApi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/credentials/icons/Zscaler.svg", "badgeIconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": [], "subcategories": {}, "alias": [], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "a55dcadd-23a9-44a0-8a51-9a31f31e6267", "key": "n8n-nodes-base.httpRequest", "subcategory": "Helpers", "properties": {"displayName": "HTTP Request", "defaults": {"name": "HTTP Request", "color": "#0004F5"}, "description": "Makes an HTTP request and returns the response data", "name": "n8n-nodes-base.httpRequest", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.svg", "dark": "icons/n8n-nodes-base/dist/nodes/HttpRequest/httprequest.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers"]}, "alias": ["API", "Request", "URL", "Build", "cURL"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.httprequest/"}]}}}, "type": "node"}, {"uuid": "509ace0d-c227-4589-878d-47344006f5fa", "key": "n8n-nodes-base.hubspot", "subcategory": "*", "properties": {"displayName": "HubSpot", "defaults": {"name": "HubSpot"}, "description": "Starts the workflow when HubSpot events occur", "name": "n8n-nodes-base.hubspot", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Hubspot/hubspot.svg", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.hubspot/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/hubspot/"}]}}}, "type": "node"}, {"uuid": "************************************", "key": "n8n-nodes-base.humanticAi", "subcategory": "*", "properties": {"displayName": "Humantic AI", "defaults": {"name": "Humantic AI"}, "description": "Consume Humantic AI API", "name": "n8n-nodes-base.humanticAi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/HumanticAI/humanticai.svg", "outputs": ["main"], "codex": {"categories": ["Analytics"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.humanticai/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/humanticAi/"}]}}}, "type": "node"}, {"uuid": "8b388270-0dea-45db-be8d-91b36a24f3bb", "key": "n8n-nodes-base.hunter", "subcategory": "*", "properties": {"displayName": "<PERSON>", "defaults": {"name": "<PERSON>"}, "description": "Consume Hunter API", "name": "n8n-nodes-base.hunter", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Hunter/hunter.png", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.hunter/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/hunter/"}]}}}, "type": "node"}, {"uuid": "323baa7a-1418-45d7-a436-c380cd59d5c3", "key": "n8n-nodes-base.if", "subcategory": "Flow", "properties": {"displayName": "If", "defaults": {"name": "If", "color": "#408000"}, "description": "Route items to different branches (true/false)", "name": "n8n-nodes-base.if", "group": ["transform"], "icon": "fa:map-signs", "iconColor": "green", "outputs": ["main", "main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Flow"]}, "alias": ["Router", "Filter", "Condition", "Logic", "Boolean", "Branch"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.if/"}]}}}, "type": "node"}, {"uuid": "a6328892-5473-4115-8b57-8c963ff1243d", "key": "n8n-nodes-base.intercom", "subcategory": "*", "properties": {"displayName": "Intercom", "defaults": {"name": "Intercom"}, "description": "Consume Intercom API", "name": "n8n-nodes-base.intercom", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Intercom/intercom.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.intercom/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/intercom/"}]}}}, "type": "node"}, {"uuid": "076d4e7b-e797-4f75-a82c-22df6fe7a61b", "key": "n8n-nodes-base.invoiceNinja", "subcategory": "*", "properties": {"displayName": "Invoice Ninja", "defaults": {"name": "Invoice Ninja"}, "description": "Starts the workflow when Invoice Ninja events occur", "name": "n8n-nodes-base.invoiceNinja", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/InvoiceNinja/invoiceNinja.svg", "outputs": ["main"], "codex": {"categories": ["Finance & Accounting"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.invoiceninja/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/invoiceNinja/"}]}}}, "type": "node"}, {"uuid": "88683cf4-0c40-42f0-847d-472d855b99ed", "key": "n8n-nodes-base.iterable", "subcategory": "*", "properties": {"displayName": "Iterable", "defaults": {"name": "Iterable"}, "description": "Consume Iterable API", "name": "n8n-nodes-base.iterable", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Iterable/iterable.png", "outputs": ["main"], "codex": {"categories": ["Communication", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.iterable/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/iterable/"}]}}}, "type": "node"}, {"uuid": "6897f7c7-71e1-4ec3-a2c3-efd9b7f8f06d", "key": "n8n-nodes-base.jenkins", "subcategory": "*", "properties": {"displayName": "<PERSON>", "defaults": {"name": "<PERSON>"}, "description": "Consume Jenkins API", "name": "n8n-nodes-base.jenkins", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Jenkins/jenkins.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jenkins/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/jenkins/"}]}}}, "type": "node"}, {"uuid": "e2b3c36c-7d7f-4f52-81cf-20e70befcdae", "key": "n8n-nodes-base.jira", "subcategory": "*", "properties": {"displayName": "Jira Software", "defaults": {"name": "Jira Software"}, "description": "Starts the workflow when Jira events occur", "name": "n8n-nodes-base.jira", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Jira/jira.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/jira/"}]}}}, "type": "node"}, {"uuid": "70d799f6-f9ea-4c40-a04d-1bf06a86858a", "key": "n8n-nodes-base.jwt", "subcategory": "*", "properties": {"displayName": "JWT", "defaults": {"name": "JWT"}, "description": "JWT", "name": "n8n-nodes-base.jwt", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Jwt/jwt.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "alias": ["Token", "Key", "JSON", "Payload", "Sign", "Verify", "Decode"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.jwt/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/jwt/"}]}}}, "type": "node"}, {"uuid": "bc398c8f-ed7b-4a0e-a5ab-e714b172ffca", "key": "n8n-nodes-base.kafka", "subcategory": "*", "properties": {"displayName": "Kafka", "defaults": {"name": "Kafka"}, "description": "Sends messages to a Kafka topic", "name": "n8n-nodes-base.kafka", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Kafka/kafka.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Kafka/kafka.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.kafka/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/kafka/"}]}}}, "type": "node"}, {"uuid": "bb267bef-92fb-40da-bb39-09c1b54c8b25", "key": "n8n-nodes-base.keap", "subcategory": "*", "properties": {"displayName": "Keap", "defaults": {"name": "Keap"}, "description": "Starts the workflow when Infusionsoft events occur", "name": "n8n-nodes-base.keap", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Keap/keap.png", "outputs": ["main"], "codex": {"categories": ["Sales", "Communication"], "alias": ["Infusionsoft"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.keap/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/keap/"}]}}}, "type": "node"}, {"uuid": "2bdf1afa-3d33-4353-9566-b2e33c2526a0", "key": "n8n-nodes-base.kitemaker", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Consume the Kitemaker GraphQL API", "name": "n8n-nodes-base.kitemaker", "group": ["input"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Kitemaker/kitemaker.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Kitemaker/kitemaker.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.kitemaker/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/kitemaker/"}]}}}, "type": "node"}, {"uuid": "a8ae8efa-**************-3012643a8e12", "key": "n8n-nodes-base.koBoToolbox", "subcategory": "*", "properties": {"displayName": "KoBoToolbox", "defaults": {"name": "KoBoToolbox"}, "description": "Process KoBoToolbox submissions", "name": "n8n-nodes-base.koBoToolbox", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/KoBoToolbox/koBoToolbox.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.kobotoolbox/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/kobotoolbox/"}]}}}, "type": "node"}, {"uuid": "f31a8d5f-764f-4cab-bb09-9464ccb1b88b", "key": "n8n-nodes-base.ldap", "subcategory": "*", "properties": {"displayName": "Ldap", "defaults": {"name": "LDAP"}, "description": "Interact with LDAP servers", "name": "n8n-nodes-base.ldap", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Ldap/ldap.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Ldap/ldap.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development", "Developer Tools"], "alias": ["ad", "active directory"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.ldap/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/ldap/"}]}}}, "type": "node"}, {"uuid": "e856024e-6a80-4791-a69b-7784955d6bcc", "key": "n8n-nodes-base.lemlist", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Handle Lemlist events via webhooks", "name": "n8n-nodes-base.lemlist", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Lemlist/lemlist.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.lemlist/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/lemlist/"}]}}}, "type": "node"}, {"uuid": "5322bb7d-abe2-4e8b-857e-5f588b523c9d", "key": "n8n-nodes-base.line", "subcategory": "*", "properties": {"displayName": "Line", "defaults": {"name": "Line"}, "description": "Consume Line API", "name": "n8n-nodes-base.line", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Line/line.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.line/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/line/"}]}}}, "type": "node"}, {"uuid": "47f6ea94-148b-4241-adbf-96668d29e54f", "key": "n8n-nodes-base.linear", "subcategory": "*", "properties": {"displayName": "Linear", "defaults": {"name": "Linear"}, "description": "Starts the workflow when Linear events occur", "name": "n8n-nodes-base.linear", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Linear/linear.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.linear/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/linear/"}]}}}, "type": "node"}, {"uuid": "4e85779c-012d-4f1c-96a8-7a5a0bdd5e38", "key": "n8n-nodes-base.lingvaNex", "subcategory": "*", "properties": {"displayName": "LingvaNex", "defaults": {"name": "LingvaNex"}, "description": "Consume LingvaNex API", "name": "n8n-nodes-base.lingvaNex", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/LingvaNex/lingvanex.png", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.lingvanex/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/lingvaNex/"}]}}}, "type": "node"}, {"uuid": "1ef161f5-50cc-45fb-a30e-7c2e157b3ebc", "key": "n8n-nodes-base.linkedIn", "subcategory": "*", "properties": {"displayName": "LinkedIn", "defaults": {"name": "LinkedIn"}, "description": "Consume LinkedIn API", "name": "n8n-nodes-base.linkedIn", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/LinkedIn/linkedin.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.linkedin/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/linkedIn/"}]}}}, "type": "node"}, {"uuid": "b185cc1b-a926-4d50-b8df-0c08ad2fa538", "key": "n8n-nodes-base.loneScale", "subcategory": "*", "properties": {"displayName": "LoneScale", "defaults": {"name": "LoneScale"}, "description": "Trigger LoneScale Workflow", "name": "n8n-nodes-base.loneScale", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/LoneScale/loneScale.svg", "dark": "icons/n8n-nodes-base/dist/nodes/LoneScale/loneScale.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.lonescale/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/lonescale/"}]}}}, "type": "node"}, {"uuid": "db279ff5-d847-4f6c-b1c8-60d37aeb39ec", "key": "n8n-nodes-base.magento2", "subcategory": "*", "properties": {"displayName": "Magento 2", "defaults": {"name": "Magento 2"}, "description": "Consume Magento API", "name": "n8n-nodes-base.magento2", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Magento/magento.svg", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.magento2/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/magento2/"}]}}}, "type": "node"}, {"uuid": "b4cb313d-9f23-4a19-ad4f-903b317bd00a", "key": "n8n-nodes-base.mailcheck", "subcategory": "*", "properties": {"displayName": "Mailcheck", "defaults": {"name": "Mailcheck"}, "description": "Consume Mailcheck API", "name": "n8n-nodes-base.mailcheck", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mailcheck/mailcheck.svg", "outputs": ["main"], "codex": {"categories": ["Utility", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mailcheck/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mailcheck/"}]}}}, "type": "node"}, {"uuid": "9f62a1a3-992c-436f-905e-533f1e8b3f0a", "key": "n8n-nodes-base.mailchimp", "subcategory": "*", "properties": {"displayName": "Mailchimp", "defaults": {"name": "Mailchimp"}, "description": "Handle Mailchimp events via webhooks", "name": "n8n-nodes-base.mailchimp", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Mailchimp/mailchimp.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Mailchimp/mailchimp.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Marketing", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mailchimp/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mailchimp/"}]}}}, "type": "node"}, {"uuid": "fbb0d83c-ab54-4c3b-a5bd-0940231dcad9", "key": "n8n-nodes-base.mailerLite", "subcategory": "*", "properties": {"displayName": "MailerLite", "defaults": {"name": "MailerLite"}, "description": "Starts the workflow when MailerLite events occur", "name": "n8n-nodes-base.mailerLite", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MailerLite/MailerLite.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mailerlite/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mailerLite/"}]}}}, "type": "node"}, {"uuid": "84e59ec9-63a6-4a83-9be2-b318d027fcbc", "key": "n8n-nodes-base.mailgun", "subcategory": "*", "properties": {"displayName": "Mailgun", "defaults": {"name": "Mailgun"}, "description": "Sends an email via Mailgun", "name": "n8n-nodes-base.mailgun", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mailgun/mailgun.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mailgun/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mailgun/"}]}}}, "type": "node"}, {"uuid": "d8462ff9-659d-4baf-ab63-5a020ad6c5a5", "key": "n8n-nodes-base.mailjet", "subcategory": "*", "properties": {"displayName": "Mailjet", "defaults": {"name": "Mailjet"}, "description": "Handle Mailjet events via webhooks", "name": "n8n-nodes-base.mailjet", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mailjet/mailjet.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mailjet/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mailjet/"}]}}}, "type": "node"}, {"uuid": "23b303f2-0032-4953-a100-f13c36ad433c", "key": "n8n-nodes-base.mandrill", "subcategory": "*", "properties": {"displayName": "Mandrill", "defaults": {"name": "Mandrill"}, "description": "Consume Mandrill API", "name": "n8n-nodes-base.mandrill", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mandrill/mandrill.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mandrill/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mandrill/"}]}}}, "type": "node"}, {"uuid": "de5791fd-5b18-4f25-b793-c850caf88f49", "key": "n8n-nodes-base.markdown", "subcategory": "Data Transformation", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>"}, "description": "Convert data between Markdown and HTML", "name": "n8n-nodes-base.markdown", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Markdown/markdown.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Markdown/markdown.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.markdown/"}]}}}, "type": "node"}, {"uuid": "1a54c269-a3c6-41d2-94d8-855a67cb30c3", "key": "n8n-nodes-base.marketstack", "subcategory": "*", "properties": {"displayName": "Marketstack", "defaults": {"name": "Marketstack"}, "description": "Consume Marketstack API", "name": "n8n-nodes-base.marketstack", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Marketstack/marketstack.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Marketstack/marketstack.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Analytics", "Finance & Accounting"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.marketstack/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/marketstack/"}]}}}, "type": "node"}, {"uuid": "377eaed0-dfe3-49ae-9d85-bc443602cf93", "key": "n8n-nodes-base.matrix", "subcategory": "*", "properties": {"displayName": "Matrix", "defaults": {"name": "Matrix"}, "description": "Consume Matrix API", "name": "n8n-nodes-base.matrix", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Matrix/matrix.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.matrix/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/matrix/"}]}}}, "type": "node"}, {"uuid": "8d5ffb13-7285-4814-b649-09ebf561028a", "key": "n8n-nodes-base.mattermost", "subcategory": "*", "properties": {"displayName": "Mattermost", "defaults": {"name": "Mattermost"}, "description": "Sends data to Mattermost", "name": "n8n-nodes-base.mattermost", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mattermost/mattermost.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mattermost/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mattermost/"}]}}}, "type": "node"}, {"uuid": "90b56c0e-0484-4373-b5b1-52aeaca2cc5b", "key": "n8n-nodes-base.mautic", "subcategory": "*", "properties": {"displayName": "Mautic", "defaults": {"name": "Mautic"}, "description": "Handle Mautic events via webhooks", "name": "n8n-nodes-base.mautic", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mautic/mautic.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mautic/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mautic/"}]}}}, "type": "node"}, {"uuid": "59839397-d54c-4c49-bef8-4e3ed624ffad", "key": "n8n-nodes-base.medium", "subcategory": "*", "properties": {"displayName": "Medium", "defaults": {"name": "Medium"}, "description": "Consume Medium API", "name": "n8n-nodes-base.medium", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Medium/medium.png", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.medium/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/medium/"}]}}}, "type": "node"}, {"uuid": "5bdb2b91-476d-419b-8b12-9ef51d0d8915", "key": "n8n-nodes-base.merge", "subcategory": "Flow", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>"}, "description": "Merges data of multiple streams once data from both is available", "name": "n8n-nodes-base.merge", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Merge/merge.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Flow", "Data Transformation"]}, "alias": ["Join", "Concatenate", "Wait"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.merge/"}]}}}, "type": "node"}, {"uuid": "31b9f7a1-b357-4c53-94c8-5fb30ec7135d", "key": "n8n-nodes-base.messageBird", "subcategory": "*", "properties": {"displayName": "MessageBird", "defaults": {"name": "MessageBird"}, "description": "Sends SMS via MessageBird", "name": "n8n-nodes-base.messageBird", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MessageBird/messagebird.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.messagebird/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/messageBird/"}]}}}, "type": "node"}, {"uuid": "e49468ab-4cf9-4b56-84f0-cd2d7169d00e", "key": "n8n-nodes-base.metabase", "subcategory": "*", "properties": {"displayName": "Metabase", "defaults": {"name": "Metabase"}, "description": "Use the Metabase API", "name": "n8n-nodes-base.metabase", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Metabase/metabase.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.metabase/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/metabase/"}]}}}, "type": "node"}, {"uuid": "0f7085de-0f08-44cf-8d77-6a2180bf1cce", "key": "n8n-nodes-base.microsoftDynamicsCrm", "subcategory": "*", "properties": {"displayName": "Microsoft Dynamics CRM", "defaults": {"name": "Microsoft Dynamics CRM"}, "description": "Consume Microsoft Dynamics CRM API", "name": "n8n-nodes-base.microsoftDynamicsCrm", "group": ["input"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Microsoft/Dynamics/microsoftDynamicsCrm.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Microsoft/Dynamics/microsoftDynamicsCrm.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Marketing", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftdynamicscrm/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "2a9e5a39-7666-4ed9-8077-28df82dce5ac", "key": "n8n-nodes-base.microsoftEntra", "subcategory": "*", "properties": {"displayName": "Microsoft Entra ID", "defaults": {"name": "Micosoft Entra ID"}, "description": "Interact with Micosoft Entra ID API", "name": "n8n-nodes-base.microsoftEntra", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Microsoft/Entra/microsoftEntra.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Microsoft/Entra/microsoftEntra.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development", "Developer Tools"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftentra/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "03f8c827-b1b2-43e3-8575-8c8ea8d27cad", "key": "n8n-nodes-base.microsoftExcel", "subcategory": "*", "properties": {"displayName": "Microsoft Excel 365", "defaults": {"name": "Microsoft Excel 365"}, "description": "Consume Microsoft Excel API", "name": "n8n-nodes-base.microsoftExcel", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/Excel/excel.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Productivity"], "alias": ["_Excel", "Excel", "Sheet", "CSV", "Spreadsheet"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftexcel/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "c754ab14-42b7-4680-b922-50d6a6d5c83f", "key": "n8n-nodes-base.microsoftGraphSecurity", "subcategory": "*", "properties": {"displayName": "Microsoft Graph Security", "defaults": {"name": "Microsoft Graph Security"}, "description": "Consume the Microsoft Graph Security API", "name": "n8n-nodes-base.microsoftGraphSecurity", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/GraphSecurity/microsoftGraph.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftgraphsecurity/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "824d03e7-5c2c-4ea0-94a6-d4361e058954", "key": "n8n-nodes-base.microsoftOneDrive", "subcategory": "*", "properties": {"displayName": "Microsoft OneDrive", "defaults": {"name": "Microsoft OneDrive"}, "description": "Trigger for Microsoft OneDrive API.", "name": "n8n-nodes-base.microsoftOneDrive", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/OneDrive/oneDrive.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftonedrive/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "42069eaf-9067-44ba-b046-07418ce7b143", "key": "n8n-nodes-base.microsoftOutlook", "subcategory": "*", "properties": {"displayName": "Microsoft Outlook", "defaults": {"name": "Microsoft Outlook"}, "description": "Fetches emails from Microsoft Outlook and starts the workflow on specified polling intervals.", "name": "n8n-nodes-base.microsoftOutlook", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/Outlook/outlook.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL"], "subcategories": {"HITL": ["Human in the Loop"]}, "alias": ["email", "human", "form", "wait", "hitl", "approval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftoutlook/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "e3b0fd9e-7e6d-4619-b852-792ce7b58013", "key": "n8n-nodes-base.microsoftSql", "subcategory": "*", "properties": {"displayName": "Microsoft SQL", "defaults": {"name": "Microsoft SQL"}, "description": "Get, add and update data in Microsoft SQL", "name": "n8n-nodes-base.microsoftSql", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/Sql/mssql.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftsql/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoftSql/"}]}}}, "type": "node"}, {"uuid": "ebb3a145-5f29-4295-95c0-99dd43f4ea67", "key": "n8n-nodes-base.azureStorage", "subcategory": "*", "properties": {"displayName": "Azure Storage", "defaults": {"name": "Azure Storage"}, "description": "Interact with Azure Storage API", "name": "n8n-nodes-base.azureStorage", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Microsoft/Storage/azureStorage.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Microsoft/Storage/azureStorage.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.azurestorage/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "c0d57e4e-0d15-4e92-9134-00d8848d2e01", "key": "n8n-nodes-base.microsoftTeams", "subcategory": "*", "properties": {"displayName": "Microsoft Teams", "defaults": {"name": "Microsoft Teams"}, "description": "Consume Microsoft Teams API", "name": "n8n-nodes-base.microsoftTeams", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/Teams/teams.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL"], "subcategories": {"HITL": ["Human in the Loop"]}, "alias": ["human", "form", "wait", "hitl", "approval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsoftteams/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "a6b9a0d7-**************-c8c1eee7e2c7", "key": "n8n-nodes-base.microsoftToDo", "subcategory": "*", "properties": {"displayName": "Microsoft To Do", "defaults": {"name": "Microsoft To Do"}, "description": "Consume Microsoft To Do API.", "name": "n8n-nodes-base.microsoftToDo", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Microsoft/ToDo/todo.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.microsofttodo/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/microsoft/"}]}}}, "type": "node"}, {"uuid": "8ef4a2e5-c98e-4c79-a7d5-780dadc6500b", "key": "n8n-nodes-base.mindee", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>"}, "description": "Consume Mindee API", "name": "n8n-nodes-base.mindee", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Mindee/mindee.svg", "outputs": ["main"], "codex": {"categories": ["Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mindee/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mindee/"}]}}}, "type": "node"}, {"uuid": "a3cb5441-9704-4319-8fbd-d86763d34ef7", "key": "n8n-nodes-base.misp", "subcategory": "*", "properties": {"displayName": "MISP", "defaults": {"name": "MISP"}, "description": "Consume the MISP API", "name": "n8n-nodes-base.misp", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Misp/misp.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.misp/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/misp/"}]}}}, "type": "node"}, {"uuid": "c0cb3564-3eaf-4d02-b271-f6c2be1060b6", "key": "n8n-nodes-base.mocean", "subcategory": "*", "properties": {"displayName": "Mocean", "defaults": {"name": "Mocean"}, "description": "Send SMS and voice messages via Mocean", "name": "n8n-nodes-base.mocean", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Mocean/mocean.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Mocean/mocean.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mocean/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mocean/"}]}}}, "type": "node"}, {"uuid": "71237e16-64d8-40e5-9b24-6436edc345cb", "key": "n8n-nodes-base.mondayCom", "subcategory": "*", "properties": {"displayName": "Monday.com", "defaults": {"name": "Monday.com"}, "description": "Consume Monday.com API", "name": "n8n-nodes-base.mondayCom", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MondayCom/mondayCom.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mondaycom/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mondayCom/"}]}}}, "type": "node"}, {"uuid": "430307ce-ee94-4abc-8669-2729ec3478b0", "key": "n8n-nodes-base.mongoDb", "subcategory": "*", "properties": {"displayName": "MongoDB", "defaults": {"name": "MongoDB"}, "description": "Find, insert and update documents in MongoDB", "name": "n8n-nodes-base.mongoDb", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MongoDb/mongodb.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mongodb/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mongoDb/"}]}}}, "type": "node"}, {"uuid": "fd53820b-cb5d-4e53-9365-ecb21c0a6eee", "key": "n8n-nodes-base.monicaCrm", "subcategory": "*", "properties": {"displayName": "Monica CRM", "defaults": {"name": "Monica CRM"}, "description": "Consume the Monica CRM API", "name": "n8n-nodes-base.monicaCrm", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MonicaCrm/monicaCrm.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.monicacrm/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/monicaCrm/"}]}}}, "type": "node"}, {"uuid": "ed3701a2-1edf-4027-aeb1-6f042aa8c9e1", "key": "n8n-nodes-base.mqtt", "subcategory": "*", "properties": {"displayName": "MQTT", "defaults": {"name": "MQTT"}, "description": "Push messages to MQTT", "name": "n8n-nodes-base.mqtt", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MQTT/mqtt.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mqtt/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mqtt/"}]}}}, "type": "node"}, {"uuid": "fb71981d-4aa1-4d75-8f49-b341f89b3487", "key": "n8n-nodes-base.msg91", "subcategory": "*", "properties": {"displayName": "MSG91", "defaults": {"name": "MSG91"}, "description": "Sends transactional SMS via MSG91", "name": "n8n-nodes-base.msg91", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Msg91/msg91.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Msg91/msg91.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.msg91/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/msg91/"}]}}}, "type": "node"}, {"uuid": "d4a72eb4-27fa-426c-8fe9-d3dbaf211512", "key": "n8n-nodes-base.mySql", "subcategory": "*", "properties": {"displayName": "MySQL", "defaults": {"name": "MySQL"}, "description": "Get, add and update data in MySQL", "name": "n8n-nodes-base.mySql", "group": ["input"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/MySql/mysql.svg", "dark": "icons/n8n-nodes-base/dist/nodes/MySql/mysql.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.mysql/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mySql/"}]}}}, "type": "node"}, {"uuid": "15e3de07-cc45-4641-b606-fd8b52835d74", "key": "n8n-nodes-base.n8n", "subcategory": "Helpers", "properties": {"displayName": "n8n", "defaults": {"name": "n8n"}, "description": "Handle events and perform actions on your n8n instance", "name": "n8n-nodes-base.n8n", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/N8n/n8n.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers", "Other Trigger Nodes"]}, "alias": ["Workflow", "Execution"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.n8n/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/api/authentication/"}]}}}, "type": "node"}, {"uuid": "a09d4b0e-53d2-42c1-a854-9bcfb82b3562", "key": "n8n-nodes-base.n8nTrainingCustomerDatastore", "subcategory": "*", "properties": {"displayName": "Customer Datastore (n8n training)", "defaults": {"name": "Customer Datastore (n8n training)"}, "description": "Dummy node used for n8n training", "name": "n8n-nodes-base.n8nTrainingCustomerDatastore", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/N8nTrainingCustomerDatastore/n8nTrainingCustomerDatastore.svg", "dark": "icons/n8n-nodes-base/dist/nodes/N8nTrainingCustomerDatastore/n8nTrainingCustomerDatastore.dark.svg"}, "outputs": ["main"]}, "type": "node"}, {"uuid": "5e03eb89-931c-4a98-a270-4e4a28222da5", "key": "n8n-nodes-base.n8nTrainingCustomerMessenger", "subcategory": "*", "properties": {"displayName": "Customer Messenger (n8n training)", "defaults": {"name": "Customer Messenger (n8n training)"}, "description": "Dummy node used for n8n training", "name": "n8n-nodes-base.n8nTrainingCustomerMessenger", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/N8nTrainingCustomerMessenger/n8nTrainingCustomerMessenger.svg", "dark": "icons/n8n-nodes-base/dist/nodes/N8nTrainingCustomerMessenger/n8nTrainingCustomerMessenger.dark.svg"}, "outputs": ["main"]}, "type": "node"}, {"uuid": "740b49dd-ce36-408e-b988-5083923e472f", "key": "n8n-nodes-base.nasa", "subcategory": "*", "properties": {"displayName": "NASA", "defaults": {"name": "NASA"}, "description": "Retrieve data from the NASA API", "name": "n8n-nodes-base.nasa", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Nasa/nasa.png", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.nasa/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/nasa/"}]}}}, "type": "node"}, {"uuid": "cfb1fa3e-4a5d-4d4b-b78a-6678e53f7ae1", "key": "n8n-nodes-base.netlify", "subcategory": "*", "properties": {"displayName": "Netlify", "defaults": {"name": "Netlify"}, "description": "Handle netlify events via webhooks", "name": "n8n-nodes-base.netlify", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Netlify/netlify.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.netlify/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/netlify/"}]}}}, "type": "node"}, {"uuid": "2c1857b5-8351-4175-b819-9e694920f662", "key": "n8n-nodes-base.nextCloud", "subcategory": "*", "properties": {"displayName": "Nextcloud", "defaults": {"name": "Nextcloud"}, "description": "Access data on Nextcloud", "name": "n8n-nodes-base.nextCloud", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/NextCloud/nextcloud.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.nextcloud/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/nextCloud/"}]}}}, "type": "node"}, {"uuid": "9832e3be-05cf-4696-849a-9e77993b6969", "key": "n8n-nodes-base.nocoDb", "subcategory": "*", "properties": {"displayName": "NocoDB", "defaults": {"name": "NocoDB"}, "description": "Read, update, write and delete data from NocoDB", "name": "n8n-nodes-base.nocoDb", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/NocoDB/nocodb.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.nocodb/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/nocoDb/"}]}}}, "type": "node"}, {"uuid": "d81b3452-0628-4f0d-89df-b586f34d8552", "key": "n8n-nodes-base.sendInBlue", "subcategory": "*", "properties": {"displayName": "Brevo", "defaults": {"name": "Brevo"}, "description": "Starts the workflow when Brevo events occur", "name": "n8n-nodes-base.sendInBlue", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Brevo/brevo.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Communication"], "alias": ["sendinblue"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.brevo/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/brevo/"}]}}}, "type": "node"}, {"uuid": "ab3e9576-9d48-44ce-a494-883e6bd3fcfe", "key": "n8n-nodes-base.stickyNote", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>", "color": "#FFD233"}, "description": "Make your workflow easier to understand", "name": "n8n-nodes-base.stickyNote", "group": ["input"], "icon": "fa:sticky-note", "outputs": []}, "type": "node"}, {"uuid": "b185164a-1174-475c-a9fb-5b3aab2d0282", "key": "n8n-nodes-base.noOp", "subcategory": "Helpers", "properties": {"displayName": "No Operation, do nothing", "defaults": {"name": "No Operation, do nothing", "color": "#b0b0b0"}, "description": "No Operation", "name": "n8n-nodes-base.noOp", "group": ["organization"], "icon": "fa:arrow-right", "iconColor": "gray", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers"]}, "alias": ["nothing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.noop/"}]}}}, "type": "node"}, {"uuid": "b8f75090-3f3e-4155-8c60-44fd49b69c39", "key": "n8n-nodes-base.onfleet", "subcategory": "*", "properties": {"displayName": "Onfleet", "defaults": {"name": "Onfleet"}, "description": "Starts the workflow when Onfleet events occur", "name": "n8n-nodes-base.onfleet", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Onfleet/Onfleet.svg", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.onfleet/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/onfleet/"}]}}}, "type": "node"}, {"uuid": "88916552-6b79-4bc6-955c-37f92a623160", "key": "n8n-nodes-base.citrixAdc", "subcategory": "*", "properties": {"displayName": "Netscaler ADC", "defaults": {"name": "Netscaler ADC"}, "description": "Consume Netscaler ADC API", "name": "n8n-nodes-base.citrixAdc", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Netscaler/ADC/netscaler.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Netscaler/ADC/netscaler.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "alias": ["Citrix"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.netscaleradc/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/netscalerAdc/"}]}}}, "type": "node"}, {"uuid": "3b070c91-0c3c-446b-a876-2f8d9597cd4e", "key": "n8n-nodes-base.notion", "subcategory": "*", "properties": {"displayName": "Notion", "defaults": {"name": "Notion"}, "description": "Starts the workflow when Notion events occur", "name": "n8n-nodes-base.notion", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Notion/notion.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Notion/notion.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.notion/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/notion/"}]}}}, "type": "node"}, {"uuid": "67f734ae-b775-449c-acbe-6cb6b2b3b3ed", "key": "n8n-nodes-base.npm", "subcategory": "*", "properties": {"displayName": "Npm", "defaults": {"name": "npm"}, "description": "Consume NPM registry API", "name": "n8n-nodes-base.npm", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Npm/npm.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.npm/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/npm/"}]}}}, "type": "node"}, {"uuid": "************************************", "key": "n8n-nodes-base.odoo", "subcategory": "*", "properties": {"displayName": "Odoo", "defaults": {"name": "Odoo"}, "description": "Consume Odoo API", "name": "n8n-nodes-base.odoo", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Odoo/odoo.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "alias": ["ERP"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.odoo/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/odoo/"}]}}}, "type": "node"}, {"uuid": "c96946fa-c5d0-45c2-b1fe-75e92940b370", "key": "n8n-nodes-base.okta", "subcategory": "*", "properties": {"displayName": "Okta", "defaults": {"name": "Okta"}, "description": "Use the Okta API", "name": "n8n-nodes-base.okta", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Okta/Okta.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Okta/Okta.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "alias": ["authentication", "users", "Security"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.okta/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/okta/"}]}}}, "type": "node"}, {"uuid": "26f9f297-b934-4ca4-9cae-2ac904bb47d3", "key": "n8n-nodes-base.oneSimpleApi", "subcategory": "*", "properties": {"displayName": "One Simple API", "defaults": {"name": "One Simple API"}, "description": "A toolbox of no-code utilities", "name": "n8n-nodes-base.oneSimpleApi", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/OneSimpleApi/onesimpleapi.svg", "outputs": ["main"], "codex": {"categories": ["Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.onesimpleapi/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/oneSimpleApi/"}]}}}, "type": "node"}, {"uuid": "b81f9827-15c5-49ef-9d90-84a4cad41e70", "key": "n8n-nodes-base.openThesaurus", "subcategory": "*", "properties": {"displayName": "OpenThesaurus", "defaults": {"name": "OpenThesaurus"}, "description": "Get synonmns for German words using the OpenThesaurus API", "name": "n8n-nodes-base.openThesaurus", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/OpenThesaurus/openthesaurus.png", "outputs": ["main"], "codex": {"categories": ["Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.openthesaurus/"}]}}}, "type": "node"}, {"uuid": "b3620915-c823-4080-adc1-eb063e566e97", "key": "n8n-nodes-base.openWeatherMap", "subcategory": "*", "properties": {"displayName": "OpenWeatherMap", "defaults": {"name": "OpenWeatherMap"}, "description": "Gets current and future weather information", "name": "n8n-nodes-base.openWeatherMap", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/OpenWeatherMap/openWeatherMap.svg", "outputs": ["main"], "codex": {"categories": ["Miscellaneous", "Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.openweathermap/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/openWeatherMap/"}]}}}, "type": "node"}, {"uuid": "8fc49792-e745-4ec7-a224-6ede41f8b37d", "key": "n8n-nodes-base.oura", "subcategory": "*", "properties": {"displayName": "Our<PERSON>", "defaults": {"name": "Our<PERSON>"}, "description": "Consume Oura API", "name": "n8n-nodes-base.oura", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Oura/oura.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Oura/oura.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.oura/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/oura/"}]}}}, "type": "node"}, {"uuid": "ed48ca98-79c2-4b8b-9c93-5dd0f1472a2c", "key": "n8n-nodes-base.paddle", "subcategory": "*", "properties": {"displayName": "Paddle", "defaults": {"name": "Paddle"}, "description": "Consume Paddle API", "name": "n8n-nodes-base.paddle", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Paddle/paddle.png", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.paddle/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/paddle/"}]}}}, "type": "node"}, {"uuid": "3ee828f7-9adb-4fcb-a38a-9d7bebb79bec", "key": "n8n-nodes-base.pagerDuty", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "description": "Consume PagerDuty API", "name": "n8n-nodes-base.pagerDuty", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PagerDuty/pagerDuty.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.pagerduty/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/pagerDuty/"}]}}}, "type": "node"}, {"uuid": "a88e78ea-b97d-48eb-b829-e889914f3131", "key": "n8n-nodes-base.payPal", "subcategory": "*", "properties": {"displayName": "PayPal", "defaults": {"name": "PayPal"}, "description": "Handle PayPal events via webhooks", "name": "n8n-nodes-base.payPal", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PayPal/paypal.svg", "outputs": ["main"], "codex": {"categories": ["Finance & Accounting", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.paypal/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/payPal/"}]}}}, "type": "node"}, {"uuid": "a7f9b1f9-4b9e-4f17-86fd-6cdc9879ee57", "key": "n8n-nodes-base.peekalink", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Consume the Peekalink API", "name": "n8n-nodes-base.peekalink", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Peekalink/peekalink.png", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.peekalink/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/peekalink/"}]}}}, "type": "node"}, {"uuid": "b6200388-6dfd-4d86-ba11-fe04cd1f5ed8", "key": "n8n-nodes-base.phantombuster", "subcategory": "*", "properties": {"displayName": "Phantombuster", "defaults": {"name": "Phantombuster"}, "description": "Consume Phantombuster API", "name": "n8n-nodes-base.phantombuster", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Phantombuster/phantombuster.png", "outputs": ["main"], "codex": {"categories": ["Sales", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.phantombuster/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/phantombuster/"}]}}}, "type": "node"}, {"uuid": "ee7defef-8621-4139-bf7c-99fe1319bdbd", "key": "n8n-nodes-base.philipsHue", "subcategory": "*", "properties": {"displayName": "<PERSON>", "defaults": {"name": "<PERSON>"}, "description": "Consume Philips Hue API", "name": "n8n-nodes-base.philipsHue", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PhilipsHue/philipshue.png", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.philipshue/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/philipsHue/"}]}}}, "type": "node"}, {"uuid": "09d7feb4-1d2d-4fdf-b8da-8cb7f467f62b", "key": "n8n-nodes-base.pipedrive", "subcategory": "*", "properties": {"displayName": "Pipedrive", "defaults": {"name": "Pipedrive"}, "description": "Starts the workflow when Pipedrive events occur", "name": "n8n-nodes-base.pipedrive", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pipedrive/pipedrive.svg", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.pipedrive/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/pipedrive/"}]}}}, "type": "node"}, {"uuid": "f850075f-8fa5-44ab-9633-ce9f4a1ee59a", "key": "n8n-nodes-base.plivo", "subcategory": "*", "properties": {"displayName": "Plivo", "defaults": {"name": "Plivo"}, "description": "Send SMS/MMS messages or make phone calls", "name": "n8n-nodes-base.plivo", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Plivo/plivo.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.plivo/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/plivo/"}]}}}, "type": "node"}, {"uuid": "*************-4e08-ad2a-9cc3d412d76a", "key": "n8n-nodes-base.postBin", "subcategory": "*", "properties": {"displayName": "PostBin", "defaults": {"name": "PostBin"}, "description": "Consume PostBin API", "name": "n8n-nodes-base.postBin", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PostBin/postbin.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.postbin/"}]}}}, "type": "node"}, {"uuid": "266eee1b-a0eb-4390-bdfd-832460794b3c", "key": "n8n-nodes-base.postgres", "subcategory": "*", "properties": {"displayName": "Postgres", "defaults": {"name": "Postgres"}, "description": "Listens to Postgres messages", "name": "n8n-nodes-base.postgres", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Postgres/postgres.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.postgres/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/postgres/"}]}}}, "type": "node"}, {"uuid": "ee33177e-f1b6-4e5e-b66a-9b9370d53db0", "key": "n8n-nodes-base.postHog", "subcategory": "*", "properties": {"displayName": "PostHog", "defaults": {"name": "PostHog"}, "description": "Consume PostHog API", "name": "n8n-nodes-base.postHog", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/PostHog/postHog.svg", "outputs": ["main"], "codex": {"categories": ["Analytics", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.posthog/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/postHog/"}]}}}, "type": "node"}, {"uuid": "ad717b43-579f-41f9-a9e4-6f713d120b92", "key": "n8n-nodes-base.profitWell", "subcategory": "*", "properties": {"displayName": "ProfitWell", "defaults": {"name": "ProfitWell"}, "description": "Consume ProfitWell API", "name": "n8n-nodes-base.profitWell", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/ProfitWell/profitwell.svg", "dark": "icons/n8n-nodes-base/dist/nodes/ProfitWell/profitwell.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Analytics"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.profitwell/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/profitWell/"}]}}}, "type": "node"}, {"uuid": "61ef8ed4-face-4667-876b-802ce7b012aa", "key": "n8n-nodes-base.pushbullet", "subcategory": "*", "properties": {"displayName": "Pushbullet", "defaults": {"name": "Pushbullet"}, "description": "Consume Pushbullet API", "name": "n8n-nodes-base.pushbullet", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pushbullet/pushbullet.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.pushbullet/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/pushbullet/"}]}}}, "type": "node"}, {"uuid": "14ee3d7d-5fca-4c89-90c7-23bcf521729e", "key": "n8n-nodes-base.pushcut", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Starts the workflow when Pushcut events occur", "name": "n8n-nodes-base.pushcut", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pushcut/pushcut.png", "outputs": ["main"], "codex": {"categories": ["Communication", "Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.pushcut/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/pushcut/"}]}}}, "type": "node"}, {"uuid": "85e2084d-641a-4758-acb0-3b8acc0de5db", "key": "n8n-nodes-base.pushover", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Consume Pushover API", "name": "n8n-nodes-base.pushover", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Pushover/pushover.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.pushover/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/pushover/"}]}}}, "type": "node"}, {"uuid": "029fedf8-8b62-447a-bc4d-9d63a51b3d53", "key": "n8n-nodes-base.questDb", "subcategory": "*", "properties": {"displayName": "QuestDB", "defaults": {"name": "QuestDB"}, "description": "Get, add and update data in QuestDB", "name": "n8n-nodes-base.questDb", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/QuestDb/questdb.png", "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.questdb/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/questDb/"}]}}}, "type": "node"}, {"uuid": "6187e65e-582a-4cca-acd4-6799e2a4ef37", "key": "n8n-nodes-base.quickbase", "subcategory": "*", "properties": {"displayName": "Quick Base", "defaults": {"name": "Quick Base"}, "description": "Integrate with the Quick Base RESTful API", "name": "n8n-nodes-base.quickbase", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/QuickBase/quickbase.png", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.quickbase/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/quickbase/"}]}}}, "type": "node"}, {"uuid": "3e1a8158-bae4-49e5-9852-4ffa632b2a55", "key": "n8n-nodes-base.quickbooks", "subcategory": "*", "properties": {"displayName": "QuickBooks Online", "defaults": {"name": "QuickBooks Online"}, "description": "Consume the QuickBooks Online API", "name": "n8n-nodes-base.quickbooks", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/QuickBooks/quickbooks.svg", "outputs": ["main"], "codex": {"categories": ["Finance & Accounting"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.quickbooks/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/quickbooks/"}]}}}, "type": "node"}, {"uuid": "d5de314e-a86d-4d00-8db9-1b99bfdf1a70", "key": "n8n-nodes-base.quick<PERSON><PERSON>", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Create a chart via QuickChart", "name": "n8n-nodes-base.quick<PERSON><PERSON>", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/QuickChart/quickChart.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "alias": ["image", "graph", "report", "chart", "diagram", "data", "visualize"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.quickchart/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/quickchart/"}]}}}, "type": "node"}, {"uuid": "0477268a-cb38-4dec-82d8-2ec04ff70e61", "key": "n8n-nodes-base.rabbitmq", "subcategory": "*", "properties": {"displayName": "RabbitMQ", "defaults": {"name": "RabbitMQ"}, "description": "Listens to RabbitMQ messages", "name": "n8n-nodes-base.rabbitmq", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/RabbitMQ/rabbitmq.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.rabbitmq/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/rabbitMQ/"}]}}}, "type": "node"}, {"uuid": "7f565c94-b6d6-4cb1-911a-be29c3cfc307", "key": "n8n-nodes-base.raindrop", "subcategory": "*", "properties": {"displayName": "Raindrop", "defaults": {"name": "Raindrop"}, "description": "Consume the Raindrop API", "name": "n8n-nodes-base.raindrop", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Raindrop/raindrop.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.raindrop/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/raindrop/"}]}}}, "type": "node"}, {"uuid": "b165cce3-aca6-4cc3-a88a-86894b922fda", "key": "n8n-nodes-base.reddit", "subcategory": "*", "properties": {"displayName": "Reddit", "defaults": {"name": "Reddit"}, "description": "Consume the Reddit API", "name": "n8n-nodes-base.reddit", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Reddit/reddit.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.reddit/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/reddit/"}]}}}, "type": "node"}, {"uuid": "43d538f5-1202-43c5-b489-8ec01d20a512", "key": "n8n-nodes-base.redis", "subcategory": "*", "properties": {"displayName": "Redis", "defaults": {"name": "Redis"}, "description": "Subscribe to redis channel", "name": "n8n-nodes-base.redis", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Redis/redis.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.redis/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/redis/"}]}}}, "type": "node"}, {"uuid": "670f8bab-db7c-42f5-b610-f2929124e897", "key": "n8n-nodes-base.rename<PERSON><PERSON>s", "subcategory": "Data Transformation", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>", "color": "#772244"}, "description": "Update item field names", "name": "n8n-nodes-base.rename<PERSON><PERSON>s", "group": ["transform"], "icon": "fa:edit", "iconColor": "crimson", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.renamekeys/"}]}}}, "type": "node"}, {"uuid": "0b80fe9a-a82c-4d8c-ab2c-78776f85e262", "key": "n8n-nodes-base.respondToWebhook", "subcategory": "Helpers", "properties": {"displayName": "Respond to Webhook", "defaults": {"name": "Respond to Webhook"}, "description": "Returns data for Webhook", "name": "n8n-nodes-base.respondToWebhook", "group": ["transform"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/RespondToWebhook/webhook.svg", "dark": "icons/n8n-nodes-base/dist/nodes/RespondToWebhook/webhook.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Core <PERSON>", "Utility"], "subcategories": {"Core Nodes": ["Helpers"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.respondtowebhook/"}]}}}, "type": "node"}, {"uuid": "48d24d60-ec49-4eab-b5aa-7f4bf465d968", "key": "n8n-nodes-base.rocketchat", "subcategory": "*", "properties": {"displayName": "RocketChat", "defaults": {"name": "RocketChat"}, "description": "Consume RocketChat API", "name": "n8n-nodes-base.rocketchat", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Rocketchat/rocketchat.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.rocketchat/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/rocketchat/"}]}}}, "type": "node"}, {"uuid": "95407dda-6ad7-4a8c-939f-faf27396eb4b", "key": "n8n-nodes-base.rssFeedRead", "subcategory": "*", "properties": {"displayName": "RSS Read", "defaults": {"name": "RSS Read", "color": "#b02020"}, "description": "Reads data from an RSS Feed", "name": "n8n-nodes-base.rssFeedRead", "group": ["input"], "icon": "fa:rss", "iconColor": "orange-red", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.rssfeedread/"}]}}}, "type": "node"}, {"uuid": "2f18e450-5d82-44e4-9755-695b667b7667", "key": "n8n-nodes-base.rundeck", "subcategory": "*", "properties": {"displayName": "Rundeck", "defaults": {"name": "Rundeck"}, "description": "Manage Rundeck API", "name": "n8n-nodes-base.rundeck", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Rundeck/rundeck.png", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.rundeck/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/rundeck/"}]}}}, "type": "node"}, {"uuid": "821caacb-158b-42df-8b29-e03dc973098c", "key": "n8n-nodes-base.s3", "subcategory": "*", "properties": {"displayName": "S3", "defaults": {"name": "S3"}, "description": "Sends data to any S3-compatible service", "name": "n8n-nodes-base.s3", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/S3/s3.png", "outputs": ["main"], "codex": {"categories": ["Development", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.s3/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/s3/"}]}}}, "type": "node"}, {"uuid": "44b00e33-9abf-4265-be13-8f880837c591", "key": "n8n-nodes-base.salesforce", "subcategory": "*", "properties": {"displayName": "Salesforce", "defaults": {"name": "Salesforce"}, "description": "Fetches data from Salesforce and starts the workflow on specified polling intervals.", "name": "n8n-nodes-base.salesforce", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Salesforce/salesforce.svg", "outputs": ["main"], "codex": {"categories": ["Sales", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.salesforce/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/salesforce/"}]}}}, "type": "node"}, {"uuid": "be05d8e9-68aa-487e-a4c7-5a4931403f8d", "key": "n8n-nodes-base.salesmate", "subcategory": "*", "properties": {"displayName": "Salesmate", "defaults": {"name": "Salesmate"}, "description": "Consume Salesmate API", "name": "n8n-nodes-base.salesmate", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Salesmate/salesmate.png", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.salesmate/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/salesmate/"}]}}}, "type": "node"}, {"uuid": "************************************", "key": "n8n-nodes-base.seaTable", "subcategory": "*", "properties": {"displayName": "SeaTable", "defaults": {"name": "SeaTable"}, "description": "Starts the workflow when SeaTable events occur", "name": "n8n-nodes-base.seaTable", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SeaTable/seaTable.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.seatable/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/seaTable/"}]}}}, "type": "node"}, {"uuid": "d857b04f-8607-445a-b7a4-7f3fc1ea471b", "key": "n8n-nodes-base.securityScorecard", "subcategory": "*", "properties": {"displayName": "SecurityScorecard", "defaults": {"name": "SecurityScorecard"}, "description": "Consume SecurityScorecard API", "name": "n8n-nodes-base.securityScorecard", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SecurityScorecard/securityScorecard.svg", "outputs": ["main"], "codex": {"categories": ["Analytics"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.securityscorecard/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/securityScorecard/"}]}}}, "type": "node"}, {"uuid": "8f6ae151-eb32-4d44-a9ad-497286ae1526", "key": "n8n-nodes-base.segment", "subcategory": "*", "properties": {"displayName": "Segment", "defaults": {"name": "Segment"}, "description": "Consume Segment API", "name": "n8n-nodes-base.segment", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Segment/segment.svg", "outputs": ["main"], "codex": {"categories": ["Analytics", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.segment/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/segment/"}]}}}, "type": "node"}, {"uuid": "abd3164b-928f-44cb-a7e2-ac10dbd6e4f9", "key": "n8n-nodes-base.sendGrid", "subcategory": "*", "properties": {"displayName": "SendGrid", "defaults": {"name": "SendGrid"}, "description": "Consume SendGrid API", "name": "n8n-nodes-base.sendGrid", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SendGrid/sendGrid.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.sendgrid/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/sendGrid/"}]}}}, "type": "node"}, {"uuid": "82c2ee75-8e23-4c15-86a0-1bdc7b3b389e", "key": "n8n-nodes-base.sendy", "subcategory": "*", "properties": {"displayName": "Sendy", "defaults": {"name": "Sendy"}, "description": "Consume Sendy API", "name": "n8n-nodes-base.sendy", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Sendy/sendy.png", "outputs": ["main"], "codex": {"categories": ["Communication", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.sendy/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/sendy/"}]}}}, "type": "node"}, {"uuid": "c2f83347-9b4d-4b97-a2d2-2c19a7b4c64c", "key": "n8n-nodes-base.sentryIo", "subcategory": "*", "properties": {"displayName": "Sentry.io", "defaults": {"name": "Sentry.io"}, "description": "Consume Sentry.io API", "name": "n8n-nodes-base.sentryIo", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/SentryIo/sentryio.svg", "dark": "icons/n8n-nodes-base/dist/nodes/SentryIo/sentryio.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.sentryio/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/sentryIo/"}]}}}, "type": "node"}, {"uuid": "5c59e7ed-1197-4793-b023-a5b1c552d64c", "key": "n8n-nodes-base.serviceNow", "subcategory": "*", "properties": {"displayName": "ServiceNow", "defaults": {"name": "ServiceNow"}, "description": "Consume ServiceNow API", "name": "n8n-nodes-base.serviceNow", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/ServiceNow/servicenow.svg", "outputs": ["main"], "codex": {"categories": ["Productivity", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.servicenow/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/serviceNow/"}]}}}, "type": "node"}, {"uuid": "db982941-ddc5-44e4-accd-e65560de5d19", "key": "n8n-nodes-base.set", "subcategory": "Data Transformation", "properties": {"displayName": "<PERSON> (Set)", "defaults": {"name": "<PERSON>"}, "description": "Modify, add, or remove item fields", "name": "n8n-nodes-base.set", "group": ["input"], "icon": "fa:pen", "iconColor": "blue", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["Set", "JS", "JSON", "Filter", "Transform", "Map"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.set/"}]}}}, "type": "node"}, {"uuid": "6635bae5-c74a-412e-9696-3a26a563e8f5", "key": "n8n-nodes-base.shopify", "subcategory": "*", "properties": {"displayName": "Shopify", "defaults": {"name": "Shopify"}, "description": "Handle Shopify events via webhooks", "name": "n8n-nodes-base.shopify", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Shopify/shopify.svg", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.shopify/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/shopify/"}]}}}, "type": "node"}, {"uuid": "7306b224-c560-41b5-98a3-77148b1b556a", "key": "n8n-nodes-base.signl4", "subcategory": "*", "properties": {"displayName": "SIGNL4", "defaults": {"name": "SIGNL4"}, "description": "Consume SIGNL4 API", "name": "n8n-nodes-base.signl4", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Signl4/signl4.png", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.signl4/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/signl4/"}]}}}, "type": "node"}, {"uuid": "8d9d5632-fea4-4833-9986-98f5dae0db5e", "key": "n8n-nodes-base.slack", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>ck", "defaults": {"name": "<PERSON><PERSON>ck"}, "description": "Handle Slack events via webhooks", "name": "n8n-nodes-base.slack", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Slack/slack.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL"], "subcategories": {"HITL": ["Human in the Loop"]}, "alias": ["human", "form", "wait", "hitl", "approval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.slack/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/slack/"}]}}}, "type": "node"}, {"uuid": "e4a18e56-3101-4c7c-aca0-ec39c9c7810b", "key": "n8n-nodes-base.sms77", "subcategory": "*", "properties": {"displayName": "seven", "defaults": {"name": "seven"}, "description": "Send SMS and make text-to-speech calls", "name": "n8n-nodes-base.sms77", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Sms77/seven.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "alias": ["SMS", "Sms77"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.sms77/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/sms77/"}]}}}, "type": "node"}, {"uuid": "fe040481-ff95-4e30-8241-37ce6c714c00", "key": "n8n-nodes-base.snowflake", "subcategory": "*", "properties": {"displayName": "Snowflake", "defaults": {"name": "Snowflake"}, "description": "Get, add and update data in Snowflake", "name": "n8n-nodes-base.snowflake", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Snowflake/snowflake.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.snowflake/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/snowflake/"}]}}}, "type": "node"}, {"uuid": "08c9fa90-f01d-4c3a-94a6-8088883dcadc", "key": "n8n-nodes-base.splitInBatches", "subcategory": "Flow", "properties": {"displayName": "Loop Over Items (Split in Batches)", "defaults": {"name": "Loop Over Items", "color": "#007755"}, "description": "Split data into batches and iterate over each batch", "name": "n8n-nodes-base.splitInBatches", "group": ["organization"], "icon": "fa:sync", "iconColor": "dark-green", "outputs": ["main", "main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Flow"]}, "alias": ["Loop", "Concatenate", "<PERSON><PERSON>", "Split", "Split In Batches"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.splitinbatches/"}]}}}, "type": "node"}, {"uuid": "e91cd031-39c3-44b3-8533-124b16e60e5d", "key": "n8n-nodes-base.splunk", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Consume the Splunk Enterprise API", "name": "n8n-nodes-base.splunk", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Splunk/splunk.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.splunk/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/splunk/"}]}}}, "type": "node"}, {"uuid": "2122fede-cb40-48e8-bdb2-ba5d490a82ca", "key": "n8n-nodes-base.spontit", "subcategory": "*", "properties": {"displayName": "Spontit", "defaults": {"name": "Spontit"}, "description": "Consume Spontit API", "name": "n8n-nodes-base.spontit", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Spontit/spontit.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.spontit/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/spontit/"}]}}}, "type": "node"}, {"uuid": "8f019232-5b88-418b-8db9-9352f543f62e", "key": "n8n-nodes-base.spotify", "subcategory": "*", "properties": {"displayName": "Spotify", "defaults": {"name": "Spotify"}, "description": "Access public song data via the Spotify API", "name": "n8n-nodes-base.spotify", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Spotify/spotify.svg", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "alias": ["Music", "Song"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.spotify/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/spotify/"}]}}}, "type": "node"}, {"uuid": "02b86200-938f-42a6-9d48-76c6112f18fd", "key": "n8n-nodes-base.ssh", "subcategory": "*", "properties": {"displayName": "SSH", "defaults": {"name": "SSH", "color": "#000000"}, "description": "Execute commands via SSH", "name": "n8n-nodes-base.ssh", "group": ["input"], "icon": "fa:terminal", "iconColor": "black", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>", "Development"], "subcategories": ["Helpers"], "alias": ["remote"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.ssh/"}]}}}, "type": "node"}, {"uuid": "0696b4c7-ffc5-43fc-89b6-7c412e7b87ae", "key": "n8n-nodes-base.stackby", "subcategory": "*", "properties": {"displayName": "Stackby", "defaults": {"name": "Stackby"}, "description": "Read, write, and delete data in Stackby", "name": "n8n-nodes-base.stackby", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Stackby/stackby.png", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.stackby/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/stackby/"}]}}}, "type": "node"}, {"uuid": "567493b3-9ec5-4cf1-9a45-496b5a1a67ff", "key": "n8n-nodes-base.stopAndError", "subcategory": "Flow", "properties": {"displayName": "Stop and Error", "defaults": {"name": "Stop and Error", "color": "#ff0000"}, "description": "Throw an error in the workflow", "name": "n8n-nodes-base.stopAndError", "group": ["input"], "icon": "fa:exclamation-triangle", "iconColor": "red", "outputs": [], "codex": {"categories": ["Core <PERSON>", "Utility"], "subcategories": {"Core Nodes": ["Flow"]}, "alias": ["Throw error", "Error", "Exception"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.stopanderror/"}]}}}, "type": "node"}, {"uuid": "91369b31-5e77-49aa-93e8-9288da5220bf", "key": "n8n-nodes-base.storyblok", "subcategory": "*", "properties": {"displayName": "Storyblok", "defaults": {"name": "Storyblok"}, "description": "Consume Storyblok API", "name": "n8n-nodes-base.storyblok", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Storyblok/storyblok.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.storyblok/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/storyblok/"}]}}}, "type": "node"}, {"uuid": "c865489b-e20c-4ffd-b214-d4faf5b0215b", "key": "n8n-nodes-base.strapi", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Consume Strapi API", "name": "n8n-nodes-base.strapi", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Strapi/strapi.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.strapi/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/strapi/"}]}}}, "type": "node"}, {"uuid": "5c6c266f-7c06-4d40-b665-a1c4f4d3c5fb", "key": "n8n-nodes-base.strava", "subcategory": "*", "properties": {"displayName": "Strava", "defaults": {"name": "Strava"}, "description": "Starts the workflow when Strava events occur", "name": "n8n-nodes-base.strava", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Strava/strava.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.strava/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/strava/"}]}}}, "type": "node"}, {"uuid": "cf47b97a-b69a-4066-9bcf-b8868b8a6716", "key": "n8n-nodes-base.stripe", "subcategory": "*", "properties": {"displayName": "Stripe", "defaults": {"name": "Stripe"}, "description": "Handle Stripe events via webhooks", "name": "n8n-nodes-base.stripe", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Stripe/stripe.svg", "outputs": ["main"], "codex": {"categories": ["Finance & Accounting", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.stripe/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/stripe/"}]}}}, "type": "node"}, {"uuid": "ae119f8e-314f-4dea-a7eb-99df68343a66", "key": "n8n-nodes-base.supabase", "subcategory": "*", "properties": {"displayName": "Supabase", "defaults": {"name": "Supabase"}, "description": "Add, get, delete and update data in a table", "name": "n8n-nodes-base.supabase", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Supabase/supabase.svg", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.supabase/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/supabase/"}]}}}, "type": "node"}, {"uuid": "23a1e844-bcca-48d6-8a8a-71cd39e7de3a", "key": "n8n-nodes-base.switch", "subcategory": "Flow", "properties": {"displayName": "Switch", "defaults": {"name": "Switch", "color": "#506000"}, "description": "Route items depending on defined expression or rules", "name": "n8n-nodes-base.switch", "group": ["transform"], "icon": "fa:map-signs", "iconColor": "light-blue", "outputs": "={{((parameters) => {\n    const mode = parameters.mode;\n    if (mode === 'expression') {\n        return Array.from({ length: parameters.numberOutputs }, (_, i) => ({\n            type: `${\"main\"}`,\n            displayName: i.toString(),\n        }));\n    }\n    else {\n        const rules = parameters.rules?.values ?? [];\n        const ruleOutputs = rules.map((rule, index) => {\n            return {\n                type: `${\"main\"}`,\n                displayName: rule.outputKey || index.toString(),\n            };\n        });\n        if (parameters.options?.fallbackOutput === 'extra') {\n            const renameFallbackOutput = parameters.options?.renameFallbackOutput;\n            ruleOutputs.push({\n                type: `${\"main\"}`,\n                displayName: renameFallbackOutput || 'Fallback',\n            });\n        }\n        return ruleOutputs;\n    }\n})($parameter)}}", "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Flow"]}, "alias": ["Router", "If", "Path", "Filter", "Condition", "Logic", "Branch", "Case"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.switch/"}]}}}, "type": "node"}, {"uuid": "79a7b10d-e54d-4c09-90be-f5e04d42b7c5", "key": "n8n-nodes-base.syncroMsp", "subcategory": "*", "properties": {"displayName": "SyncroMSP", "defaults": {"name": "SyncroMSP"}, "description": "Gets data from SyncroMSP", "name": "n8n-nodes-base.syncroMsp", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SyncroMSP/syncromsp.png", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.syncromsp/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/syncroMsp/"}]}}}, "type": "node"}, {"uuid": "e8c8f3ef-c090-4a30-8bc3-1309c38cbb00", "key": "n8n-nodes-base.taiga", "subcategory": "*", "properties": {"displayName": "Taiga", "defaults": {"name": "Taiga"}, "description": "Handle Taiga events via webhook", "name": "n8n-nodes-base.taiga", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Taiga/taiga.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.taiga/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/taiga/"}]}}}, "type": "node"}, {"uuid": "fd71af39-1183-475e-a0bb-7f86d0dc8f48", "key": "n8n-nodes-base.tapfiliate", "subcategory": "*", "properties": {"displayName": "Tapfiliate", "defaults": {"name": "Tapfiliate"}, "description": "Consume Tapfiliate API", "name": "n8n-nodes-base.tapfiliate", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Tapfiliate/tapfiliate.svg", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.tapfiliate/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/tapfiliate/"}]}}}, "type": "node"}, {"uuid": "f23c3094-5f27-43af-bec5-0b498d8cd922", "key": "n8n-nodes-base.telegram", "subcategory": "*", "properties": {"displayName": "Telegram", "defaults": {"name": "Telegram"}, "description": "Starts the workflow on a Telegram update", "name": "n8n-nodes-base.telegram", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Telegram/telegram.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL"], "subcategories": {"HITL": ["Human in the Loop"]}, "alias": ["human", "form", "wait", "hitl", "approval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.telegram/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/telegram/"}]}}}, "type": "node"}, {"uuid": "29826905-51bd-49e3-8f06-40fab20edc32", "key": "n8n-nodes-base.theHiveProject", "subcategory": "*", "properties": {"displayName": "TheHive 5", "defaults": {"name": "TheHive 5"}, "description": "Starts the workflow when TheHive events occur", "name": "n8n-nodes-base.theHiveProject", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/TheHiveProject/thehiveproject.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "alias": ["Security", "Monitoring", "Incident", "Response", "<PERSON><PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.thehive5/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/thehive5/"}]}}}, "type": "node"}, {"uuid": "818b6d7a-b985-4009-9153-25f9e7b5c6a1", "key": "n8n-nodes-base.theHive", "subcategory": "*", "properties": {"displayName": "TheHive", "defaults": {"name": "TheHive"}, "description": "Starts the workflow when TheHive events occur", "name": "n8n-nodes-base.theHive", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/TheHive/thehive.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.thehive/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/thehive/"}]}}}, "type": "node"}, {"uuid": "0f6fcfe1-60c5-4505-b5c8-e5285716ba2d", "key": "n8n-nodes-base.timescaleDb", "subcategory": "*", "properties": {"displayName": "TimescaleDB", "defaults": {"name": "TimescaleDB"}, "description": "Add and update data in TimescaleDB", "name": "n8n-nodes-base.timescaleDb", "group": ["input"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/TimescaleDb/timescaleDb.svg", "dark": "icons/n8n-nodes-base/dist/nodes/TimescaleDb/timescaleDb.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Data & Storage", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.timescaledb/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/timescaleDb/"}]}}}, "type": "node"}, {"uuid": "b4ca2f10-2d72-44e4-877e-aea0825799b2", "key": "n8n-nodes-base.todoist", "subcategory": "*", "properties": {"displayName": "Todoist", "defaults": {"name": "Todoist"}, "description": "Consume Todoist API", "name": "n8n-nodes-base.todoist", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Todoist/todoist.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.todoist/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/todoist/"}]}}}, "type": "node"}, {"uuid": "0bade2a6-609d-4a10-814e-e63dd77ac0e0", "key": "n8n-nodes-base.totp", "subcategory": "*", "properties": {"displayName": "TOTP", "defaults": {"name": "TOTP"}, "description": "Generate a time-based one-time password", "name": "n8n-nodes-base.totp", "group": ["transform"], "icon": "fa:fingerprint", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": ["Helpers"], "alias": ["2FA", "MFA", "authentication", "Security", "OTP", "password", "multi", "factor"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.totp/"}]}}}, "type": "node"}, {"uuid": "037fe477-7466-4de3-819a-ec3605a8d853", "key": "n8n-nodes-base.travisCi", "subcategory": "*", "properties": {"displayName": "TravisCI", "defaults": {"name": "TravisCI"}, "description": "Consume TravisCI API", "name": "n8n-nodes-base.travisCi", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/TravisCi/travisci.png", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.travisci/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/travisCi/"}]}}}, "type": "node"}, {"uuid": "b3edebd3-39e4-4c93-ab74-a49de9f3b5f3", "key": "n8n-nodes-base.trello", "subcategory": "*", "properties": {"displayName": "Trello", "defaults": {"name": "Trello"}, "description": "Starts the workflow when Trello events occur", "name": "n8n-nodes-base.trello", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Trello/trello.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.trello/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/trello/"}]}}}, "type": "node"}, {"uuid": "b09c8d91-2ecb-4a4d-b38b-b1f31f73d109", "key": "n8n-nodes-base.twake", "subcategory": "*", "properties": {"displayName": "Twake", "defaults": {"name": "Twake"}, "description": "Consume Twake API", "name": "n8n-nodes-base.twake", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Twake/twake.png", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.twake/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/twake/"}]}}}, "type": "node"}, {"uuid": "e7bcae16-f1e0-47ae-943d-debd24482447", "key": "n8n-nodes-base.twilio", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Starts the workflow on a Twilio update", "name": "n8n-nodes-base.twilio", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Twilio/twilio.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "alias": ["SMS", "Phone", "Voice"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.twilio/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/twilio/"}]}}}, "type": "node"}, {"uuid": "ba8d2b4d-1bd3-4e40-9a91-dd9d49a6fbf4", "key": "n8n-nodes-base.twist", "subcategory": "*", "properties": {"displayName": "Twist", "defaults": {"name": "Twist"}, "description": "Consume Twist API", "name": "n8n-nodes-base.twist", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Twist/twist.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.twist/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/twist/"}]}}}, "type": "node"}, {"uuid": "95c19ddc-**************-0b9c892409b0", "key": "n8n-nodes-base.twitter", "subcategory": "*", "properties": {"displayName": "X (Formerly Twitter)", "defaults": {"name": "X"}, "description": "Post, like, and search tweets, send messages, search users, and add users to lists", "name": "n8n-nodes-base.twitter", "group": ["output"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Twitter/x.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Twitter/x.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Marketing"], "alias": ["Tweet", "Twitter", "X", "X API"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.twitter/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/twitter/"}]}}}, "type": "node"}, {"uuid": "d1e1179a-b844-4ca0-b670-e7df10935994", "key": "n8n-nodes-base.unleashedSoftware", "subcategory": "*", "properties": {"displayName": "Unleashed Software", "defaults": {"name": "Unleashed Software"}, "description": "Consume Unleashed Software API", "name": "n8n-nodes-base.unleashedSoftware", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/UnleashedSoftware/unleashedSoftware.png", "outputs": ["main"], "codex": {"categories": ["Sales", "Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.unleashedsoftware/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/unleashedSoftware/"}]}}}, "type": "node"}, {"uuid": "e0b503db-8025-43ab-bf18-b5a85a3a3c03", "key": "n8n-nodes-base.uplead", "subcategory": "*", "properties": {"displayName": "Uplead", "defaults": {"name": "Uplead"}, "description": "Consume Uplead API", "name": "n8n-nodes-base.uplead", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Uplead/uplead.png", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.uplead/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/uplead/"}]}}}, "type": "node"}, {"uuid": "872506f8-e5af-4838-bf3e-f84f6113f446", "key": "n8n-nodes-base.uproc", "subcategory": "*", "properties": {"displayName": "uProc", "defaults": {"name": "uProc"}, "description": "Consume uProc API", "name": "n8n-nodes-base.uproc", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/UProc/uproc.png", "outputs": ["main"], "codex": {"categories": ["Data & Storage"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.uproc/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/uProc/"}]}}}, "type": "node"}, {"uuid": "deb68854-1724-4a22-b520-eec5602e60a9", "key": "n8n-nodes-base.uptimeRobot", "subcategory": "*", "properties": {"displayName": "UptimeRobot", "defaults": {"name": "UptimeRobot"}, "description": "Consume UptimeRobot API", "name": "n8n-nodes-base.uptimeRobot", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/UptimeRobot/uptimerobot.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.uptimerobot/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/uptimeRobot/"}]}}}, "type": "node"}, {"uuid": "6c96c94b-66bc-4d6a-95fa-5e37d6873c82", "key": "n8n-nodes-base.urlScanIo", "subcategory": "*", "properties": {"displayName": "urlscan.io", "defaults": {"name": "urlscan.io"}, "description": "Provides various utilities for monitoring websites like health checks or screenshots", "name": "n8n-nodes-base.urlScanIo", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/UrlScanIo/urlScanIo.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Utility"], "alias": ["Scrape"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.urlscanio/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/urlScanIo/"}]}}}, "type": "node"}, {"uuid": "************************************", "key": "n8n-nodes-base.vero", "subcategory": "*", "properties": {"displayName": "Vero", "defaults": {"name": "Vero"}, "description": "Consume Vero API", "name": "n8n-nodes-base.vero", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Vero/vero.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.vero/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/vero/"}]}}}, "type": "node"}, {"uuid": "a65ef077-ec7f-4ab1-aa09-985d75552f86", "key": "n8n-nodes-base.venafiTlsProtectCloud", "subcategory": "*", "properties": {"displayName": "Venafi TLS Protect Cloud", "defaults": {"name": "Venafi TLS Protect Cloud"}, "description": "Starts the workflow when <PERSON><PERSON><PERSON> events occur", "name": "n8n-nodes-base.venafiTlsProtectCloud", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Venafi/venafi.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.venafitlsprotectcloud/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/venafiTlsProtectCloud/"}]}}}, "type": "node"}, {"uuid": "90ddc79d-0668-4949-975e-9cf7c31c8325", "key": "n8n-nodes-base.venafiTlsProtectDatacenter", "subcategory": "*", "properties": {"displayName": "Venafi TLS Protect Datacenter", "defaults": {"name": "Venafi TLS Protect Datacenter"}, "description": "Consume Venafi TLS Protect Datacenter", "name": "n8n-nodes-base.venafiTlsProtectDatacenter", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Venafi/venafi.svg", "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.venafitlsprotectdatacenter/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/venafiTlsProtectDatacenter/"}]}}}, "type": "node"}, {"uuid": "a124af8f-d320-4e2b-8be3-cf9955fccbd4", "key": "n8n-nodes-base.von<PERSON>", "subcategory": "*", "properties": {"displayName": "Vonage", "defaults": {"name": "Vonage"}, "description": "Consume Vonage API", "name": "n8n-nodes-base.von<PERSON>", "group": ["input"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Vonage/vonage.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Vonage/vonage.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Communication"], "alias": ["SMS"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.vonage/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/vonage/"}]}}}, "type": "node"}, {"uuid": "8ff55de2-d94b-4825-9333-e55a5fda2488", "key": "n8n-nodes-base.wait", "subcategory": "Helpers", "properties": {"displayName": "Wait", "defaults": {"name": "Wait", "color": "#804050"}, "description": "Wait before continue with execution", "name": "n8n-nodes-base.wait", "group": ["organization"], "icon": "fa:pause-circle", "iconColor": "crimson", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers", "Flow"]}, "alias": ["pause", "sleep", "delay", "timeout"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.wait/"}]}}}, "type": "node"}, {"uuid": "b43da899-8985-4f4b-b74a-ca65850affa5", "key": "n8n-nodes-base.webflow", "subcategory": "*", "properties": {"displayName": "Webflow", "defaults": {"name": "Webflow"}, "description": "Handle Webflow events via webhooks", "name": "n8n-nodes-base.webflow", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Webflow/webflow.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.webflow/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/webflow/"}]}}}, "type": "node"}, {"uuid": "c446580b-edb2-4c82-8be0-a58d401457f0", "key": "n8n-nodes-base.wekan", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>"}, "description": "Consume Wekan API", "name": "n8n-nodes-base.wekan", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Wekan/wekan.svg", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.wekan/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/wekan/"}]}}}, "type": "node"}, {"uuid": "9a8d9ee6-109b-493e-995c-08209ff8cfd4", "key": "n8n-nodes-base.whatsApp", "subcategory": "*", "properties": {"displayName": "WhatsApp Business Cloud", "defaults": {"name": "WhatsApp Business Cloud"}, "description": "Handle WhatsApp events via webhooks", "name": "n8n-nodes-base.whatsApp", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/WhatsApp/whatsapp.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "HITL"], "subcategories": {"HITL": ["Human in the Loop"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.whatsapp/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/whatsapp/"}]}}}, "type": "node"}, {"uuid": "2eeccd3b-8da5-46ba-bf5f-93a97b58c9ea", "key": "n8n-nodes-base.wise", "subcategory": "*", "properties": {"displayName": "<PERSON>", "defaults": {"name": "<PERSON>"}, "description": "Handle Wise events via webhooks", "name": "n8n-nodes-base.wise", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Wise/wise.svg", "outputs": ["main"], "codex": {"categories": ["Finance & Accounting"], "alias": ["<PERSON><PERSON><PERSON><PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.wise/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/wise/"}]}}}, "type": "node"}, {"uuid": "1e647d07-23d0-42cd-b213-45ed91582df0", "key": "n8n-nodes-base.wooCommerce", "subcategory": "*", "properties": {"displayName": "WooCommerce", "defaults": {"name": "WooCommerce"}, "description": "Handle WooCommerce events via webhooks", "name": "n8n-nodes-base.wooCommerce", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/WooCommerce/wooCommerce.svg", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.woocommerce/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/wooCommerce/"}]}}}, "type": "node"}, {"uuid": "45cfc20f-0175-401d-bc46-1d5696257cfd", "key": "n8n-nodes-base.wordpress", "subcategory": "*", "properties": {"displayName": "Wordpress", "defaults": {"name": "Wordpress"}, "description": "Consume Wordpress API", "name": "n8n-nodes-base.wordpress", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Wordpress/wordpress.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.wordpress/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/wordpress/"}]}}}, "type": "node"}, {"uuid": "dc4187f0-e902-4814-94b2-9da0b821590a", "key": "n8n-nodes-base.xero", "subcategory": "*", "properties": {"displayName": "Xero", "defaults": {"name": "Xero"}, "description": "Consume Xero API", "name": "n8n-nodes-base.xero", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Xero/xero.svg", "outputs": ["main"], "codex": {"categories": ["Finance & Accounting"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.xero/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/xero/"}]}}}, "type": "node"}, {"uuid": "d0e2007a-fba4-43d6-88ed-7b41e929320a", "key": "n8n-nodes-base.xml", "subcategory": "Data Transformation", "properties": {"displayName": "XML", "defaults": {"name": "XML", "color": "#333377"}, "description": "Convert data from and to XML", "name": "n8n-nodes-base.xml", "group": ["transform"], "icon": "fa:file-code", "iconColor": "purple", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["Parse"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.xml/"}]}}}, "type": "node"}, {"uuid": "3cac5300-3585-4ac1-b089-0c25983b5b4f", "key": "n8n-nodes-base.yourls", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>"}, "description": "Consume Yourls API", "name": "n8n-nodes-base.yourls", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Yourls/yourls.png", "outputs": ["main"], "codex": {"categories": ["Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.yourls/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/yourls/"}]}}}, "type": "node"}, {"uuid": "fa30a094-cab1-4ac2-8975-34a1d845cc3d", "key": "n8n-nodes-base.zammad", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Consume the Zammad API", "name": "n8n-nodes-base.zammad", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zammad/zammad.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.zammad/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/zammad/"}]}}}, "type": "node"}, {"uuid": "6e979d92-183d-40e5-aead-49422b2e6b17", "key": "n8n-nodes-base.zendesk", "subcategory": "*", "properties": {"displayName": "Zendesk", "defaults": {"name": "Zendesk"}, "description": "Handle Zendesk events via webhooks", "name": "n8n-nodes-base.zendesk", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zendesk/zendesk.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.zendesk/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/zendesk/"}]}}}, "type": "node"}, {"uuid": "4b84414b-c7c6-465c-9057-37f6c50ef801", "key": "n8n-nodes-base.zohoCrm", "subcategory": "*", "properties": {"displayName": "Zoho CRM", "defaults": {"name": "Zoho CRM"}, "description": "Consume Zoho CRM API", "name": "n8n-nodes-base.zohoCrm", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zoho/zoho.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.zohocrm/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/zoho/"}]}}}, "type": "node"}, {"uuid": "e9c914ce-bf72-4c61-9798-a50317cc3676", "key": "n8n-nodes-base.zoom", "subcategory": "*", "properties": {"displayName": "Zoom", "defaults": {"name": "Zoom"}, "description": "Consume Zoom API", "name": "n8n-nodes-base.zoom", "group": ["input"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zoom/zoom.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.zoom/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/zoom/"}]}}}, "type": "node"}, {"uuid": "a7e09cb5-b4b2-44b3-be23-23510829faf6", "key": "n8n-nodes-base.zulip", "subcategory": "*", "properties": {"displayName": "Zulip", "defaults": {"name": "Zulip"}, "description": "Consume Zulip API", "name": "n8n-nodes-base.zulip", "group": ["output"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Zulip/zulip.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.zulip/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/zulip/"}]}}}, "type": "node"}, {"uuid": "dfddb866-fce3-440c-a00f-2d9be3e0845a", "key": "n8n-nodes-base.aggregate", "subcategory": "Data Transformation", "properties": {"displayName": "Aggregate", "defaults": {"name": "Aggregate"}, "description": "Combine a field from many items into a list in a single item", "name": "n8n-nodes-base.aggregate", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Transform/Aggregate/aggregate.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["Aggregate", "Combine", "<PERSON><PERSON>", "Transform", "Array", "List", "<PERSON><PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.aggregate/"}]}}}, "type": "node"}, {"uuid": "bc62217d-1e11-4d0d-b7aa-858312c16e98", "key": "n8n-nodes-base.limit", "subcategory": "Data Transformation", "properties": {"displayName": "Limit", "defaults": {"name": "Limit"}, "description": "Restrict the number of items", "name": "n8n-nodes-base.limit", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Transform/Limit/limit.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["Limit", "Remove", "Slice", "Transform", "Array", "List", "<PERSON><PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.limit/"}]}}}, "type": "node"}, {"uuid": "8e3d64cf-d088-45f8-8c44-5cb84a902f59", "key": "n8n-nodes-base.removeDuplicates", "subcategory": "Data Transformation", "properties": {"displayName": "Remove Duplicates", "defaults": {"name": "Remove Duplicates"}, "description": "Delete items with matching field values", "name": "n8n-nodes-base.removeDuplicates", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Transform/RemoveDuplicates/removeDuplicates.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["<PERSON><PERSON><PERSON>", "Deduplicate", "Duplicates", "Remove", "Unique", "Transform", "Array", "List", "<PERSON><PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.removeduplicates/"}]}}}, "type": "node"}, {"uuid": "60106b25-26e2-4457-89bf-1fe941dc3493", "key": "n8n-nodes-base.splitOut", "subcategory": "Data Transformation", "properties": {"displayName": "Split Out", "defaults": {"name": "Split Out"}, "description": "Turn a list inside item(s) into separate items", "name": "n8n-nodes-base.splitOut", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Transform/SplitOut/splitOut.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["Split", "Nested", "Transform", "Array", "List", "<PERSON><PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.splitout/"}]}}}, "type": "node"}, {"uuid": "687a2478-0dd6-4628-b948-e2d6e1f1ed88", "key": "n8n-nodes-base.sort", "subcategory": "Data Transformation", "properties": {"displayName": "Sort", "defaults": {"name": "Sort"}, "description": "Change items order", "name": "n8n-nodes-base.sort", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Transform/Sort/sort.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["Sort", "Order", "Transform", "Array", "List", "<PERSON><PERSON>", "Random"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.sort/"}]}}}, "type": "node"}, {"uuid": "06dd4039-5374-4fb8-a389-d84e93a33321", "key": "n8n-nodes-base.summarize", "subcategory": "Data Transformation", "properties": {"displayName": "Summarize", "defaults": {"name": "Summarize"}, "description": "Sum, count, max, etc. across items", "name": "n8n-nodes-base.summarize", "group": ["transform"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Transform/Summarize/summarize.svg", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Data Transformation"]}, "alias": ["Append", "Array", "Average", "Concatenate", "Count", "Group", "<PERSON><PERSON>", "List", "Max", "Min", "Pivot", "Sum", "Summarise", "Summarize", "Transform", "Unique"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.summarize/"}]}}}, "type": "node"}, {"uuid": "691e02d6-45e8-4533-9f3c-d516576c05ed", "key": "@n8n/n8n-nodes-langchain.openAi", "subcategory": "*", "properties": {"displayName": "OpenAI", "defaults": {"name": "OpenAI"}, "description": "Message an assistant or GPT, analyze images, generate audio, etc.", "name": "@n8n/n8n-nodes-langchain.openAi", "group": ["transform"], "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vendors/OpenAi/openAi.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vendors/OpenAi/openAi.dark.svg"}, "outputs": ["main"], "codex": {"alias": ["<PERSON><PERSON><PERSON><PERSON>", "ChatGPT", "Dall<PERSON>", "whisper", "audio", "transcribe", "tts", "assistant"], "categories": ["AI"], "subcategories": {"AI": ["Agents", "Miscellaneous", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-langchain.openai/"}]}}}, "type": "node"}, {"uuid": "a442a806-47b0-4d7d-8824-3a831839ec2e", "key": "@n8n/n8n-nodes-langchain.agent", "subcategory": "*", "properties": {"displayName": "AI Agent", "defaults": {"name": "AI Agent", "color": "#404040"}, "description": "Generates an action plan and executes it. Can use external tools.", "name": "@n8n/n8n-nodes-langchain.agent", "group": ["transform"], "icon": "fa:robot", "iconColor": "black", "outputs": ["main"], "codex": {"alias": ["<PERSON><PERSON><PERSON><PERSON>", "Cha<PERSON>", "Conversational", "Plan and Execute", "ReAct", "Tools"], "categories": ["AI"], "subcategories": {"AI": ["Agents", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.agent/"}]}}}, "type": "node"}, {"uuid": "699fd59d-39b8-4ffb-8d5d-77698ab3c3ac", "key": "@n8n/n8n-nodes-langchain.chainSummarization", "subcategory": "*", "properties": {"displayName": "Summarization Chain", "defaults": {"name": "Summarization Chain", "color": "#909298"}, "description": "Transforms text into a concise summary", "name": "@n8n/n8n-nodes-langchain.chainSummarization", "group": ["transform"], "icon": "fa:link", "iconColor": "black", "outputs": ["main"], "codex": {"alias": ["<PERSON><PERSON><PERSON><PERSON>"], "categories": ["AI"], "subcategories": {"AI": ["Chains", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainsummarization/"}]}}}, "type": "node"}, {"uuid": "a5869a2e-8151-49d2-8679-b3af3a114653", "key": "@n8n/n8n-nodes-langchain.chainLlm", "subcategory": "*", "properties": {"displayName": "Basic LLM Chain", "defaults": {"name": "Basic LLM Chain", "color": "#909298"}, "description": "A simple chain to prompt a large language model", "name": "@n8n/n8n-nodes-langchain.chainLlm", "group": ["transform"], "icon": "fa:link", "iconColor": "black", "outputs": ["main"], "codex": {"alias": ["<PERSON><PERSON><PERSON><PERSON>"], "categories": ["AI"], "subcategories": {"AI": ["Chains", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainllm/"}]}}}, "type": "node"}, {"uuid": "0ea1604f-507e-429f-a393-302f7cada0b3", "key": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "subcategory": "*", "properties": {"displayName": "Question and Answer Chain", "defaults": {"name": "Question and Answer Chain", "color": "#909298"}, "description": "Answer questions about retrieved documents", "name": "@n8n/n8n-nodes-langchain.chainRetrievalQa", "group": ["transform"], "icon": "fa:link", "iconColor": "black", "outputs": ["main"], "codex": {"alias": ["<PERSON><PERSON><PERSON><PERSON>"], "categories": ["AI"], "subcategories": {"AI": ["Chains", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.chainretrievalqa/"}]}}}, "type": "node"}, {"uuid": "23da6af5-53d7-4155-aefe-2282811a330f", "key": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "subcategory": "*", "properties": {"displayName": "Sentiment Analysis", "defaults": {"name": "Sentiment Analysis"}, "description": "Analyze the sentiment of your text", "name": "@n8n/n8n-nodes-langchain.sentimentAnalysis", "group": ["transform"], "icon": "fa:balance-scale-left", "iconColor": "black", "outputs": "={{((parameters, defaultCategories) => {\n    const options = (parameters?.options ?? {});\n    const categories = options?.categories ?? defaultCategories;\n    const categoriesArray = categories.split(',').map((cat) => cat.trim());\n    const ret = categoriesArray.map((cat) => ({ type: \"main\", displayName: cat }));\n    return ret;\n})($parameter, \"Positive, Neutral, Negative\")}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Chains", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.sentimentanalysis/"}]}}}, "type": "node"}, {"uuid": "240ddfa7-5658-48f1-89cb-8987ccd46f30", "key": "@n8n/n8n-nodes-langchain.informationExtractor", "subcategory": "*", "properties": {"displayName": "Information Extractor", "defaults": {"name": "Information Extractor"}, "description": "Extract information from text in a structured format", "name": "@n8n/n8n-nodes-langchain.informationExtractor", "group": ["transform"], "icon": "fa:project-diagram", "iconColor": "black", "outputs": ["main"], "codex": {"alias": ["NER", "parse", "parsing", "JSON", "data extraction", "structured"], "categories": ["AI"], "subcategories": {"AI": ["Chains", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.information-extractor/"}]}}}, "type": "node"}, {"uuid": "af73d3a7-0f26-4a6c-bb41-75b58ff08d3f", "key": "@n8n/n8n-nodes-langchain.textClassifier", "subcategory": "*", "properties": {"displayName": "Text Classifier", "defaults": {"name": "Text Classifier"}, "description": "Classify your text into distinct categories", "name": "@n8n/n8n-nodes-langchain.textClassifier", "group": ["transform"], "icon": "fa:tags", "iconColor": "black", "outputs": "={{((parameters) => {\n    const categories = parameters.categories?.categories ?? [];\n    const fallback = parameters.options?.fallback;\n    const ret = categories.map((cat) => {\n        return { type: \"main\", displayName: cat.category };\n    });\n    if (fallback === 'other')\n        ret.push({ type: \"main\", displayName: 'Other' });\n    return ret;\n})($parameter)}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Chains", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.text-classifier/"}]}}}, "type": "node"}, {"uuid": "69d8a48d-cfcc-41cf-ac6c-544c7057f7da", "key": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "subcategory": "*", "properties": {"displayName": "Anthropic <PERSON>", "defaults": {"name": "Anthropic <PERSON>"}, "description": "Language Model Anthropic", "name": "@n8n/n8n-nodes-langchain.lmChatAnthropic", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMChatAnthropic/anthropic.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatanthropic/"}]}, "alias": ["claude", "sonnet", "opus"]}}, "type": "node"}, {"uuid": "686ccc88-d3c6-439d-8d7f-d59a35daff8f", "key": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "subcategory": "*", "properties": {"displayName": "Azure OpenAI Chat Model", "defaults": {"name": "Azure OpenAI Chat Model"}, "description": "For advanced usage with an AI chain", "name": "@n8n/n8n-nodes-langchain.lmChatAzureOpenAi", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatAzureOpenAi/azure.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatazureopenai/"}]}}}, "type": "node"}, {"uuid": "3e646cc1-a890-448a-ad61-333890a0288a", "key": "@n8n/n8n-nodes-langchain.lmChatAwsBedrock", "subcategory": "*", "properties": {"displayName": "AWS Bedrock Chat Model", "defaults": {"name": "AWS Bedrock Chat Model"}, "description": "Language Model AWS Bedrock", "name": "@n8n/n8n-nodes-langchain.lmChatAwsBedrock", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatAwsBedrock/bedrock.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatawsbedrock/"}]}}}, "type": "node"}, {"uuid": "44e0a995-a192-48ea-8a17-4c4003e07ee5", "key": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "subcategory": "*", "properties": {"displayName": "DeepSeek Chat Model", "defaults": {"name": "DeepSeek Chat Model"}, "description": "For advanced usage with an AI chain", "name": "@n8n/n8n-nodes-langchain.lmChatDeepSeek", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatDeepSeek/deepseek.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatdeepseek/"}]}}}, "type": "node"}, {"uuid": "de7aedde-d455-4980-bb7a-409d4fb00f10", "key": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "subcategory": "*", "properties": {"displayName": "Google Gemini Chat Model", "defaults": {"name": "Google Gemini Chat Model"}, "description": "Chat Model Google Gemini", "name": "@n8n/n8n-nodes-langchain.lmChatGoogleGemini", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatGoogleGemini/google.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatgooglegemini/"}]}}}, "type": "node"}, {"uuid": "6338ca7f-ee4c-4ea0-929b-e8107ea447b3", "key": "@n8n/n8n-nodes-langchain.lmChatGoogleVertex", "subcategory": "*", "properties": {"displayName": "Google Vertex Chat Model", "defaults": {"name": "Google Vertex Chat Model"}, "description": "Chat Model Google Vertex", "name": "@n8n/n8n-nodes-langchain.lmChatGoogleVertex", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatGoogleVertex/google.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatgooglevertex/"}]}}}, "type": "node"}, {"uuid": "8a515977-fac9-4a69-a43e-f9425fd12b4a", "key": "@n8n/n8n-nodes-langchain.lmChatGroq", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Language Model Groq", "name": "@n8n/n8n-nodes-langchain.lmChatGroq", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatGroq/groq.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatgroq/"}]}}}, "type": "node"}, {"uuid": "c006bd5f-79d1-4f44-bad1-d27b6002f9c9", "key": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "subcategory": "*", "properties": {"displayName": "Mistral Cloud Chat Model", "defaults": {"name": "Mistral Cloud Chat Model"}, "description": "For advanced usage with an AI chain", "name": "@n8n/n8n-nodes-langchain.lmChatMistralCloud", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatMistralCloud/mistral.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatmistralcloud/"}]}}}, "type": "node"}, {"uuid": "1107ebd8-2575-4620-a769-69d3cc1e8114", "key": "@n8n/n8n-nodes-langchain.lmChatOllama", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON> Chat Model", "defaults": {"name": "<PERSON><PERSON><PERSON> Chat Model"}, "description": "Language Model Ollama", "name": "@n8n/n8n-nodes-langchain.lmChatOllama", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMChatOllama/ollama.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatollama/"}]}}}, "type": "node"}, {"uuid": "b6cc7994-94fc-4a3d-958b-490a8f53ff28", "key": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "subcategory": "*", "properties": {"displayName": "OpenRouter <PERSON>", "defaults": {"name": "OpenRouter <PERSON>"}, "description": "For advanced usage with an AI chain", "name": "@n8n/n8n-nodes-langchain.lmChatOpenRouter", "group": ["transform"], "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatOpenRouter/openrouter.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LmChatOpenRouter/openrouter.dark.svg"}, "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatopenrouter/"}]}}}, "type": "node"}, {"uuid": "5f62082d-87bd-4fe6-a6fb-3899d78d238e", "key": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "subcategory": "*", "properties": {"displayName": "OpenAI Chat Model", "defaults": {"name": "OpenAI Chat Model"}, "description": "For advanced usage with an AI chain", "name": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "group": ["transform"], "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMChatOpenAi/openAiLight.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMChatOpenAi/openAiLight.dark.svg"}, "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Chat Models (Recommended)"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmchatopenai/"}]}}}, "type": "node"}, {"uuid": "27e66a71-9774-46c2-ae1e-de0d490ded5b", "key": "@n8n/n8n-nodes-langchain.lmCohere", "subcategory": "*", "properties": {"displayName": "Cohere Model", "defaults": {"name": "Cohere Model"}, "description": "Language Model Cohere", "name": "@n8n/n8n-nodes-langchain.lmCohere", "group": ["transform"], "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMCohere/cohere.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMCohere/cohere.dark.svg"}, "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Text Completion Models"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmcohere/"}]}}}, "type": "node"}, {"uuid": "3defba5a-5605-48ee-917f-4c6c47616bbf", "key": "@n8n/n8n-nodes-langchain.lmOllama", "subcategory": "*", "properties": {"displayName": "Ollama Model", "defaults": {"name": "Ollama Model"}, "description": "Language Model Ollama", "name": "@n8n/n8n-nodes-langchain.lmOllama", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMOllama/ollama.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Text Completion Models"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmollama/"}]}}}, "type": "node"}, {"uuid": "738a3e23-fd1e-4dc2-b7d6-c6fa481971dd", "key": "@n8n/n8n-nodes-langchain.lmOpenHuggingFaceInference", "subcategory": "*", "properties": {"displayName": "Hugging Face Inference Model", "defaults": {"name": "Hugging Face Inference Model"}, "description": "Language Model HuggingFaceInference", "name": "@n8n/n8n-nodes-langchain.lmOpenHuggingFaceInference", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/llms/LMOpenHuggingFaceInference/huggingface.svg", "outputs": ["ai_languageModel"], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Language Models", "Root Nodes"], "Language Models": ["Text Completion Models"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.lmopenhuggingfaceinference/"}]}}}, "type": "node"}, {"uuid": "47276465-2cb1-4f6f-97d2-a3823375f3bd", "key": "@n8n/n8n-nodes-langchain.memoryManager", "subcategory": "*", "properties": {"displayName": "Chat Memory Manager", "defaults": {"name": "Chat Memory Manager"}, "description": "Manage chat messages memory and use it in the workflow", "name": "@n8n/n8n-nodes-langchain.memoryManager", "group": ["transform"], "icon": "fa:database", "iconColor": "black", "outputs": [{"displayName": "", "type": "main"}], "codex": {"categories": ["AI"], "subcategories": {"AI": ["Miscellaneous", "Root Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/sub-nodes/n8n-nodes-langchain.memorymanager/"}]}}}, "type": "node"}, {"uuid": "5c83505b-e77e-4414-af19-d7f541c08c2c", "key": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "subcategory": "*", "properties": {"displayName": "Simple Vector Store", "defaults": {"name": "Simple Vector Store"}, "description": "Work with your data in a Simple Vector Store. Don't use this for production usage.", "name": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "group": ["transform"], "icon": "fa:database", "iconColor": "black", "outputs": "={{\n\t\t\t((parameters) => {\n\t\t\t\tconst mode = parameters?.mode ?? 'retrieve';\n\n\t\t\t\tif (mode === 'retrieve-as-tool') {\n\t\t\t\t\treturn [{ displayName: \"Tool\", type: \"ai_tool\"}]\n\t\t\t\t}\n\n\t\t\t\tif (mode === 'retrieve') {\n\t\t\t\t\treturn [{ displayName: \"Vector Store\", type: \"ai_vectorStore\"}]\n\t\t\t\t}\n\t\t\t\treturn [{ displayName: \"\", type: \"main\"}]\n\t\t\t})($parameter)\n\t\t}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Vector Stores", "Tools", "Root Nodes"], "Tools": ["Other Tools"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoreinmemory/"}]}}}, "type": "node"}, {"uuid": "50ea80a3-14a5-4ea9-96b4-45d442a6ad6d", "key": "@n8n/n8n-nodes-langchain.vectorStoreMongoDBAtlas", "subcategory": "*", "properties": {"displayName": "MongoDB Atlas Vector Store", "defaults": {"name": "MongoDB Atlas Vector Store"}, "description": "Work with your data in MongoDB Atlas Vector Store", "name": "@n8n/n8n-nodes-langchain.vectorStoreMongoDBAtlas", "group": ["transform"], "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStoreMongoDBAtlas/mongodb.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStoreMongoDBAtlas/mongodb.dark.svg"}, "outputs": "={{\n\t\t\t((parameters) => {\n\t\t\t\tconst mode = parameters?.mode ?? 'retrieve';\n\n\t\t\t\tif (mode === 'retrieve-as-tool') {\n\t\t\t\t\treturn [{ displayName: \"Tool\", type: \"ai_tool\"}]\n\t\t\t\t}\n\n\t\t\t\tif (mode === 'retrieve') {\n\t\t\t\t\treturn [{ displayName: \"Vector Store\", type: \"ai_vectorStore\"}]\n\t\t\t\t}\n\t\t\t\treturn [{ displayName: \"\", type: \"main\"}]\n\t\t\t})($parameter)\n\t\t}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Vector Stores", "Tools", "Root Nodes"], "Tools": ["Other Tools"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoremongodbatlas/"}]}}}, "type": "node"}, {"uuid": "fb24df35-5047-4514-b6c4-21707b169819", "key": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "subcategory": "*", "properties": {"displayName": "Postgres PGVector Store", "defaults": {"name": "Postgres PGVector Store"}, "description": "Work with your data in Postgresql with the PGVector extension", "name": "@n8n/n8n-nodes-langchain.vectorStorePGVector", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStorePGVector/postgres.svg", "outputs": "={{\n\t\t\t((parameters) => {\n\t\t\t\tconst mode = parameters?.mode ?? 'retrieve';\n\n\t\t\t\tif (mode === 'retrieve-as-tool') {\n\t\t\t\t\treturn [{ displayName: \"Tool\", type: \"ai_tool\"}]\n\t\t\t\t}\n\n\t\t\t\tif (mode === 'retrieve') {\n\t\t\t\t\treturn [{ displayName: \"Vector Store\", type: \"ai_vectorStore\"}]\n\t\t\t\t}\n\t\t\t\treturn [{ displayName: \"\", type: \"main\"}]\n\t\t\t})($parameter)\n\t\t}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Vector Stores", "Tools", "Root Nodes"], "Tools": ["Other Tools"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstorepgvector/"}]}}}, "type": "node"}, {"uuid": "a0ef2ecf-3d57-4a27-a699-e8291c1dc9eb", "key": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "subcategory": "*", "properties": {"displayName": "Pinecone Vector Store", "defaults": {"name": "Pinecone Vector Store"}, "description": "Work with your data in Pinecone Vector Store", "name": "@n8n/n8n-nodes-langchain.vectorStorePinecone", "group": ["transform"], "iconUrl": {"light": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStorePinecone/pinecone.svg", "dark": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStorePinecone/pinecone.dark.svg"}, "outputs": "={{\n\t\t\t((parameters) => {\n\t\t\t\tconst mode = parameters?.mode ?? 'retrieve';\n\n\t\t\t\tif (mode === 'retrieve-as-tool') {\n\t\t\t\t\treturn [{ displayName: \"Tool\", type: \"ai_tool\"}]\n\t\t\t\t}\n\n\t\t\t\tif (mode === 'retrieve') {\n\t\t\t\t\treturn [{ displayName: \"Vector Store\", type: \"ai_vectorStore\"}]\n\t\t\t\t}\n\t\t\t\treturn [{ displayName: \"\", type: \"main\"}]\n\t\t\t})($parameter)\n\t\t}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Vector Stores", "Tools", "Root Nodes"], "Tools": ["Other Tools"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstorepinecone/"}]}}}, "type": "node"}, {"uuid": "4a66b9f5-10ec-4623-9868-7cca3a8e281a", "key": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "subcategory": "*", "properties": {"displayName": "Qdrant Vector Store", "defaults": {"name": "Qdrant Vector Store"}, "description": "Work with your data in a Qdrant collection", "name": "@n8n/n8n-nodes-langchain.vectorStoreQdrant", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStoreQdrant/qdrant.svg", "outputs": "={{\n\t\t\t((parameters) => {\n\t\t\t\tconst mode = parameters?.mode ?? 'retrieve';\n\n\t\t\t\tif (mode === 'retrieve-as-tool') {\n\t\t\t\t\treturn [{ displayName: \"Tool\", type: \"ai_tool\"}]\n\t\t\t\t}\n\n\t\t\t\tif (mode === 'retrieve') {\n\t\t\t\t\treturn [{ displayName: \"Vector Store\", type: \"ai_vectorStore\"}]\n\t\t\t\t}\n\t\t\t\treturn [{ displayName: \"\", type: \"main\"}]\n\t\t\t})($parameter)\n\t\t}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Vector Stores", "Tools", "Root Nodes"], "Tools": ["Other Tools"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoreqdrant/"}]}}}, "type": "node"}, {"uuid": "53c6d1ce-96ac-454e-9523-22136306bb6c", "key": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "subcategory": "*", "properties": {"displayName": "Supabase Vector Store", "defaults": {"name": "Supabase Vector Store"}, "description": "Work with your data in Supabase Vector Store", "name": "@n8n/n8n-nodes-langchain.vectorStoreSupabase", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStoreSupabase/supabase.svg", "outputs": "={{\n\t\t\t((parameters) => {\n\t\t\t\tconst mode = parameters?.mode ?? 'retrieve';\n\n\t\t\t\tif (mode === 'retrieve-as-tool') {\n\t\t\t\t\treturn [{ displayName: \"Tool\", type: \"ai_tool\"}]\n\t\t\t\t}\n\n\t\t\t\tif (mode === 'retrieve') {\n\t\t\t\t\treturn [{ displayName: \"Vector Store\", type: \"ai_vectorStore\"}]\n\t\t\t\t}\n\t\t\t\treturn [{ displayName: \"\", type: \"main\"}]\n\t\t\t})($parameter)\n\t\t}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Vector Stores", "Tools", "Root Nodes"], "Tools": ["Other Tools"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstoresupabase/"}]}}}, "type": "node"}, {"uuid": "6c073633-c1b0-4fa8-a4b2-126fdf8fdb40", "key": "@n8n/n8n-nodes-langchain.vectorStoreZep", "subcategory": "*", "properties": {"displayName": "Zep Vector Store", "defaults": {"name": "Zep Vector Store"}, "description": "Work with your data in Zep Vector Store", "name": "@n8n/n8n-nodes-langchain.vectorStoreZep", "group": ["transform"], "iconUrl": "icons/@n8n/n8n-nodes-langchain/dist/nodes/vector_store/VectorStoreZep/zep.png", "outputs": "={{\n\t\t\t((parameters) => {\n\t\t\t\tconst mode = parameters?.mode ?? 'retrieve';\n\n\t\t\t\tif (mode === 'retrieve-as-tool') {\n\t\t\t\t\treturn [{ displayName: \"Tool\", type: \"ai_tool\"}]\n\t\t\t\t}\n\n\t\t\t\tif (mode === 'retrieve') {\n\t\t\t\t\treturn [{ displayName: \"Vector Store\", type: \"ai_vectorStore\"}]\n\t\t\t\t}\n\t\t\t\treturn [{ displayName: \"\", type: \"main\"}]\n\t\t\t})($parameter)\n\t\t}}", "codex": {"categories": ["AI"], "subcategories": {"AI": ["Vector Stores", "Tools", "Root Nodes"], "Tools": ["Other Tools"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/cluster-nodes/root-nodes/n8n-nodes-langchain.vectorstorezep/"}]}}}, "type": "node"}, {"uuid": "7b06e32c-fa15-48bb-b83c-22ae1c6b4d10", "key": "n8n-nodes-base.acuitySchedulingTrigger", "subcategory": "*", "properties": {"displayName": "Acuity <PERSON>ulin<PERSON>", "defaults": {"name": "Acuity <PERSON>ulin<PERSON>"}, "description": "Handle Acuity Scheduling events via webhooks", "name": "n8n-nodes-base.acuitySchedulingTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/AcuityScheduling/acuityScheduling.png", "outputs": ["main"], "codex": {"categories": ["Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.acuityschedulingtrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/acuityScheduling/"}]}}}, "type": "node"}, {"uuid": "12edbc20-cd7d-4e55-828b-5bfa5deb1abf", "key": "n8n-nodes-base.amqpTrigger", "subcategory": "*", "properties": {"displayName": "AMQP Trigger", "defaults": {"name": "AMQP Trigger"}, "description": "Listens to AMQP 1.0 Messages", "name": "n8n-nodes-base.amqpTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Amqp/amqp.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.amqptrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/amqp/"}]}}}, "type": "node"}, {"uuid": "61b77a14-b2dc-4743-af31-abfd00be7b5e", "key": "n8n-nodes-base.bitbucketTrigger", "subcategory": "*", "properties": {"displayName": "Bitbucket Trigger", "defaults": {"name": "Bitbucket Trigger"}, "description": "Handle Bitbucket events via webhooks", "name": "n8n-nodes-base.bitbucketTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Bitbucket/bitbucket.svg", "outputs": ["main"], "codex": {"categories": ["Development", "Productivity"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.bitbuckettrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/bitbucket/"}]}}}, "type": "node"}, {"uuid": "fc231bda-5ea8-4ac2-8d5f-bf3ac9ec0998", "key": "n8n-nodes-base.calTrigger", "subcategory": "*", "properties": {"displayName": "Cal.com Trigger", "defaults": {"name": "Cal.com Trigger"}, "description": "Handle Cal.com events via webhooks", "name": "n8n-nodes-base.calTrigger", "group": ["trigger"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Cal/cal.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Cal/cal.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Productivity", "Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.caltrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/cal/"}]}}}, "type": "node"}, {"uuid": "ca45a6f3-4be3-4b38-a7d6-f9f035f4fab4", "key": "n8n-nodes-base.calendlyTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Starts the workflow when Calendly events occur", "name": "n8n-nodes-base.calendlyTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Calendly/calendly.svg", "outputs": ["main"], "codex": {"categories": ["Productivity", "Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.calendlytrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/calendly/"}]}}}, "type": "node"}, {"uuid": "0bf2c77d-9d9b-4e80-8b40-9165aa4d75e7", "key": "n8n-nodes-base.emailReadImap", "subcategory": "Other Trigger Nodes", "properties": {"displayName": "<PERSON><PERSON> (IMAP)", "defaults": {"name": "<PERSON><PERSON> (IMAP)", "color": "#44AA22"}, "description": "Triggers the workflow when a new email is received", "name": "n8n-nodes-base.emailReadImap", "group": ["trigger"], "icon": "fa:inbox", "iconColor": "green", "outputs": ["main"], "codex": {"categories": ["Communication", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Other Trigger Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.emailimap/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/imap/"}]}}}, "type": "node"}, {"uuid": "ff26ab1c-5185-4e46-8be0-0f022c0cafe8", "key": "n8n-nodes-base.errorTrigger", "subcategory": "Other Trigger Nodes", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>", "color": "#0000FF"}, "description": "Triggers the workflow when another workflow has an error", "name": "n8n-nodes-base.errorTrigger", "group": ["trigger"], "icon": "fa:bug", "iconColor": "blue", "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Other Trigger Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.errortrigger/"}]}}}, "type": "node"}, {"uuid": "ac0f650a-a618-4b85-bc79-c1314592f011", "key": "n8n-nodes-base.eventbriteTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Handle Eventbrite events via webhooks", "name": "n8n-nodes-base.eventbriteTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Eventbrite/eventbrite.png", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.eventbritetrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/eventbrite/"}]}}}, "type": "node"}, {"uuid": "7f0256e8-b317-4289-b8d1-3b941904be2c", "key": "n8n-nodes-base.facebookTrigger", "subcategory": "*", "properties": {"displayName": "Facebook Trigger", "defaults": {"name": "Facebook Trigger"}, "description": "Starts the workflow when Facebook events occur", "name": "n8n-nodes-base.facebookTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Facebook/facebook.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "alias": ["FB"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.facebooktrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/credentials/facebookapp/"}]}}}, "type": "node"}, {"uuid": "d002e2ea-670b-40bb-9c3a-192b8b46dfbf", "key": "n8n-nodes-base.facebookLeadAdsTrigger", "subcategory": "*", "properties": {"displayName": "Facebook Lead <PERSON><PERSON>", "defaults": {"name": "Facebook Lead <PERSON><PERSON>"}, "description": "Handle Facebook Lead Ads events via webhooks", "name": "n8n-nodes-base.facebookLeadAdsTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/FacebookLeadAds/facebook.svg", "outputs": ["main"], "codex": {"categories": ["Marketing"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.facebookleadadstrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/credentials/facebookleadads/"}]}}}, "type": "node"}, {"uuid": "32449285-b3ff-41db-9aca-73ea322d5578", "key": "n8n-nodes-base.figmaTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON> (Beta)", "defaults": {"name": "<PERSON><PERSON> (Beta)"}, "description": "Starts the workflow when Figma events occur", "name": "n8n-nodes-base.figmaTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Figma/figma.svg", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.figmatrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/figma/"}]}}}, "type": "node"}, {"uuid": "b37c1b84-2704-4887-b716-1431d20d8ee4", "key": "n8n-nodes-base.formIoTrigger", "subcategory": "*", "properties": {"displayName": "Form.io Trigger", "defaults": {"name": "Form.io Trigger"}, "description": "Handle form.io events via webhooks", "name": "n8n-nodes-base.formIoTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/FormIo/formio.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.formiotrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/formIoTrigger/"}]}}}, "type": "node"}, {"uuid": "f8a9c650-5093-49db-9e8e-19cae3b44360", "key": "n8n-nodes-base.formstackTrigger", "subcategory": "*", "properties": {"displayName": "Formstack Trigger", "defaults": {"name": "Formstack Trigger"}, "description": "Starts the workflow on a Formstack form submission.", "name": "n8n-nodes-base.formstackTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Formstack/formstack.svg", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.formstacktrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/formstackTrigger/"}]}}}, "type": "node"}, {"uuid": "26d3e4af-4736-4016-a628-7b2138eee583", "key": "n8n-nodes-base.gumroadTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Handle Gumroad events via webhooks", "name": "n8n-nodes-base.gumroadTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Gumroad/gumroad.png", "outputs": ["main"], "codex": {"categories": ["Sales"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.gumroadtrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/gumroad/"}]}}}, "type": "node"}, {"uuid": "a8da7b8b-979f-4fc0-9aba-c44245be22a7", "key": "n8n-nodes-base.jotFormTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON><PERSON>"}, "description": "Handle JotForm events via webhooks", "name": "n8n-nodes-base.jotFormTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/JotForm/jotform.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.jotformtrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/jotForm/"}]}}}, "type": "node"}, {"uuid": "22955c64-8bb2-4653-81b1-e8b0114f1809", "key": "n8n-nodes-base.kafkaTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Consume messages from a Kafka topic", "name": "n8n-nodes-base.kafkaTrigger", "group": ["trigger"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Kafka/kafka.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Kafka/kafka.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.kafkatrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/kafka/"}]}}}, "type": "node"}, {"uuid": "8cbffa86-8db8-4226-a2cb-d192c64827db", "key": "n8n-nodes-base.localFileTrigger", "subcategory": "Files", "properties": {"displayName": "Local File Trigger", "defaults": {"name": "Local File Trigger", "color": "#404040"}, "description": "Triggers a workflow on file system changes", "name": "n8n-nodes-base.localFileTrigger", "group": ["trigger"], "icon": "fa:folder-open", "iconColor": "black", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "subcategories": {"Core Nodes": ["Files", "Other Trigger Nodes"]}, "alias": ["Watch", "Monitor"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.localfiletrigger/"}]}}}, "type": "node"}, {"uuid": "08f410b9-85fa-44ea-9b77-634fcd3c445f", "key": "n8n-nodes-base.manualTrigger", "subcategory": "*", "properties": {"displayName": "Manual Trigger", "defaults": {"name": "When clicking ‘Execute workflow’", "color": "#909298"}, "description": "Runs the flow on clicking a button in n8n", "name": "n8n-nodes-base.manualTrigger", "group": ["trigger"], "icon": "fa:mouse-pointer", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.manualworkflowtrigger/"}]}}}, "type": "node"}, {"uuid": "aa03f25c-53ca-4883-8bc3-443c21681292", "key": "n8n-nodes-base.mqttTrigger", "subcategory": "*", "properties": {"displayName": "MQ<PERSON> Trigger", "defaults": {"name": "MQ<PERSON> Trigger"}, "description": "Listens to MQTT events", "name": "n8n-nodes-base.mqttTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/MQTT/mqtt.svg", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.mqtttrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/mqtt/"}]}}}, "type": "node"}, {"uuid": "b775cf0d-6977-49a3-aa12-44722bb64bbe", "key": "n8n-nodes-base.postmarkTrigger", "subcategory": "*", "properties": {"displayName": "Postmark Trigger", "defaults": {"name": "Postmark Trigger"}, "description": "Starts the workflow when Postmark events occur", "name": "n8n-nodes-base.postmarkTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Postmark/postmark.png", "outputs": ["main"], "codex": {"categories": ["Communication", "Development"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.postmarktrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/postmark/"}]}}}, "type": "node"}, {"uuid": "************************************", "key": "n8n-nodes-base.rssFeedReadTrigger", "subcategory": "*", "properties": {"displayName": "RSS Feed <PERSON>gger", "defaults": {"name": "RSS Feed <PERSON>gger", "color": "#b02020"}, "description": "Starts a workflow when an RSS feed is updated", "name": "n8n-nodes-base.rssFeedReadTrigger", "group": ["trigger"], "icon": "fa:rss", "iconColor": "orange-red", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.rssfeedreadtrigger/"}]}}}, "type": "node"}, {"uuid": "c01a97fe-0cc0-43b0-83af-ea70541edf3b", "key": "n8n-nodes-base.scheduleTrigger", "subcategory": "*", "properties": {"displayName": "Schedule Trigger", "defaults": {"name": "Schedule Trigger", "color": "#31C49F"}, "description": "Triggers the workflow on a given schedule", "name": "n8n-nodes-base.scheduleTrigger", "group": ["trigger", "schedule"], "icon": "fa:clock", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "alias": ["Time", "Scheduler", "Polling", "<PERSON><PERSON>", "Interval"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.scheduletrigger/"}]}}}, "type": "node"}, {"uuid": "14529f1b-afb0-4a89-93af-a4f09c6f962b", "key": "n8n-nodes-base.sseTrigger", "subcategory": "Other Trigger Nodes", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON>", "color": "#225577"}, "description": "Triggers the workflow when Server-Sent Events occur", "name": "n8n-nodes-base.sseTrigger", "group": ["trigger"], "icon": "fa:cloud-download-alt", "iconColor": "dark-blue", "outputs": ["main"], "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Other Trigger Nodes"]}, "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.ssetrigger/"}]}}}, "type": "node"}, {"uuid": "02c08c23-153d-4579-9a17-508193e4b227", "key": "n8n-nodes-base.surveyMonkeyTrigger", "subcategory": "*", "properties": {"displayName": "Survey<PERSON><PERSON>key <PERSON>", "defaults": {"name": "Survey<PERSON><PERSON>key <PERSON>"}, "description": "Starts the workflow when Survey Monkey events occur", "name": "n8n-nodes-base.surveyMonkeyTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/SurveyMonkey/surveyMonkey.svg", "outputs": ["main"], "codex": {"categories": ["Marketing", "Communication"], "alias": ["Form"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.surveymonkeytrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/surveyMonkey/"}]}}}, "type": "node"}, {"uuid": "c07261c1-7503-4808-97b8-5f322523e089", "key": "n8n-nodes-base.togglTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Starts the workflow when Toggl events occur", "name": "n8n-nodes-base.togglTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Toggl/toggl.png", "outputs": ["main"], "codex": {"categories": ["Productivity", "Utility"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.toggltrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/toggl/"}]}}}, "type": "node"}, {"uuid": "0e43de8d-77b2-4e8c-a28f-da9639fc4e75", "key": "n8n-nodes-base.typeformTrigger", "subcategory": "*", "properties": {"displayName": "Typeform Trigger", "defaults": {"name": "Typeform Trigger"}, "description": "Starts the workflow on a Typeform form submission", "name": "n8n-nodes-base.typeformTrigger", "group": ["trigger"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Typeform/typeform.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Typeform/typeform.dark.svg"}, "outputs": ["main"], "codex": {"categories": ["Communication"], "alias": ["Form"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.typeformtrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/typeform/"}]}}}, "type": "node"}, {"uuid": "8b08997d-da95-4645-8d93-9f241a91161d", "key": "n8n-nodes-base.webhook", "subcategory": "Helpers", "properties": {"displayName": "Webhook", "defaults": {"name": "Webhook"}, "description": "Starts the workflow when a webhook is called", "name": "n8n-nodes-base.webhook", "group": ["trigger"], "iconUrl": {"light": "icons/n8n-nodes-base/dist/nodes/Webhook/webhook.svg", "dark": "icons/n8n-nodes-base/dist/nodes/Webhook/webhook.dark.svg"}, "outputs": "={{((parameters) => {\n    const httpMethod = parameters.httpMethod;\n    if (!Array.isArray(httpMethod))\n        return [\n            {\n                type: `${\"main\"}`,\n                displayName: httpMethod,\n            },\n        ];\n    const outputs = httpMethod.map((method) => {\n        return {\n            type: `${\"main\"}`,\n            displayName: method,\n        };\n    });\n    return outputs;\n})($parameter)}}", "codex": {"categories": ["Development", "Core <PERSON>"], "subcategories": {"Core Nodes": ["Helpers"]}, "alias": ["HTTP", "API", "Build", "WH"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.webhook/"}]}}}, "type": "node"}, {"uuid": "5fcdb35c-0e1c-4de5-825f-7b9bdca2f8f0", "key": "n8n-nodes-base.workableTrigger", "subcategory": "*", "properties": {"displayName": "Workable Trigger", "defaults": {"name": "Workable Trigger"}, "description": "Starts the workflow when Workable events occur", "name": "n8n-nodes-base.workableTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Workable/workable.png", "outputs": ["main"], "codex": {"categories": ["Miscellaneous"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.workabletrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/workable/"}]}}}, "type": "node"}, {"uuid": "c578024b-f4b5-4de4-9f7a-52be7f3a5402", "key": "n8n-nodes-base.wufooTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON><PERSON>", "defaults": {"name": "<PERSON><PERSON><PERSON>"}, "description": "Handle Wufoo events via webhooks", "name": "n8n-nodes-base.wufooTrigger", "group": ["trigger"], "iconUrl": "icons/n8n-nodes-base/dist/nodes/Wufoo/wufoo.png", "outputs": ["main"], "codex": {"categories": ["Communication"], "alias": ["Form"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/trigger-nodes/n8n-nodes-base.wufootrigger/"}], "credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/wufoo/"}]}}}, "type": "node"}, {"uuid": "8a366ddc-35b5-4f76-8cef-6b221c6b0a0a", "key": "@n8n/n8n-nodes-langchain.chatTrigger", "subcategory": "*", "properties": {"displayName": "<PERSON><PERSON>", "defaults": {"name": "When chat message received"}, "description": "Runs the workflow when an n8n generated webchat is submitted", "name": "@n8n/n8n-nodes-langchain.chatTrigger", "group": ["trigger"], "icon": "fa:comments", "iconColor": "black", "outputs": ["main"], "codex": {"categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-langchain.chattrigger/"}]}}}, "type": "node"}]