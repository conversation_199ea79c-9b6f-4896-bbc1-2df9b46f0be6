const sharedOptions = require('@n8n/eslint-config/shared');

/**
 * @type {import('@types/eslint').ESLint.ConfigData}
 */
module.exports = {
	extends: ['@n8n/eslint-config/node'],

	...sharedOptions(__dirname),

	ignorePatterns: ['jest.config.js'],

	rules: {
		'unicorn/filename-case': ['error', { case: 'kebabCase' }],
		// '@typescript-eslint/no-duplicate-imports': 'off',

		complexity: 'error',
	},
};
