# @n8n/utils

A collection of utility functions that provide common functionality for both Front-End and Back-End packages.

## Table of Contents

- [Features](#features)
- [Contributing](#contributing)
- [License](#license)

## Features

- **Reusable Logic**: Build complex, stateful functionality using modular composable functions that you can easily reuse.
- **Consistent Patterns**: Enjoy a unified approach across n8n packages, making integration and maintenance a breeze.
- **Type-Safe & Reliable**: Benefit from TypeScript support, which improves the developer experience and code robustness.
- **Universal Functionality**: Designed to work seamlessly on both the front-end and back-end.
- **Easily Testable**: A modular design that simplifies testing, maintenance, and rapid development.

## Contributing

For more details, please read our [CONTRIBUTING.md](CONTRIBUTING.md).

## License

For more details, please read our [LICENSE.md](LICENSE.md).
